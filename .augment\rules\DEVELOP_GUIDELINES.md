---
type: "manual"
description: "React/TypeScript项目开发指南"
---
# React/TypeScript 项目开发指南

## 📋 目录

1. [项目概述](#项目概述)
2. [技术栈介绍](#技术栈介绍)
3. [开发环境设置](#开发环境设置)
4. [项目结构详解](#项目结构详解)
5. [创建新页面和路由](#创建新页面和路由)
6. [组件开发规范](#组件开发规范)
7. [样式开发指南](#样式开发指南)
8. [状态管理](#状态管理)
9. [API集成](#api集成)
10. [测试指南](#测试指南)
11. [构建和部署](#构建和部署)
12. [代码规范](#代码规范)
13. [故障排除](#故障排除)

## 🎯 项目概述

本项目是一个基于React和TypeScript的现代化单页应用(SPA)，使用Vite作为构建工具，shadcn/ui作为UI组件库，Tailwind CSS作为样式框架。项目主要功能包括BBC新闻开场动画展示和英语学习页面。

## 🛠️ 技术栈介绍

### 核心技术
- **React 18** - 用于构建用户界面的JavaScript库
- **TypeScript** - JavaScript的超集，提供静态类型检查
- **Vite** - 快速的前端构建工具
- **React Router** - 客户端路由管理

### UI和样式
- **shadcn/ui** - 现代化的React组件库
- **Tailwind CSS** - 实用优先的CSS框架
- **Radix UI** - 无样式的可访问UI组件（shadcn/ui的基础）

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **TypeScript Compiler** - 类型检查

## 🚀 开发环境设置

### 前置要求
- **Node.js** >= 18.0.0
- **npm** >= 8.0.0 或 **pnpm** >= 7.0.0
- **Git** - 版本控制

### 快速开始
```bash
# 克隆项目
git clone <repository-url>
cd news-reel-opener-magic

# 安装依赖
npm install
# 或使用 pnpm
pnpm install

# 启动开发服务器
npm run dev
# 或使用 pnpm
pnpm dev

# 访问应用
# 浏览器打开 http://localhost:8080
```

### 开发服务器特性
- ⚡ **热模块替换 (HMR)** - 代码修改实时反映
- 🔍 **TypeScript 类型检查** - 实时错误提示
- 🌐 **多网络接口** - 支持局域网设备访问
- 📱 **移动端调试** - 通过局域网IP访问

## 📁 项目结构详解

```
news-reel-opener-magic/
├── src/                    # 源代码目录
│   ├── components/         # React组件
│   │   ├── BBCNewsOpening.tsx      # BBC新闻开场动画组件
│   │   └── EnglishLearningPage.tsx # 英语学习页面组件
│   ├── pages/              # 页面组件
│   │   ├── Index.tsx       # 首页组件
│   │   └── English.tsx     # 英语学习页面
│   ├── hooks/              # 自定义Hook (可选)
│   │   └── useApiCall.ts   # API调用Hook示例
│   ├── lib/                # 工具函数和配置
│   │   └── utils.ts        # 通用工具函数
│   ├── types/              # TypeScript类型定义
│   │   └── index.ts        # 全局类型定义
│   ├── assets/             # 静态资源
│   │   └── classroom-background.jpg # 背景图片
│   ├── App.tsx             # 主应用组件和路由配置
│   ├── main.tsx            # 应用入口点
│   └── index.css           # 全局样式和Tailwind导入
├── public/                 # 公共静态文件
│   ├── index.html          # HTML模板
│   └── favicon.ico         # 网站图标
├── dist/                   # 构建输出目录
├── .augment/               # Augment配置目录
│   └── rules/              # 开发规范文档
├── vite.config.ts          # Vite配置文件
├── tailwind.config.js      # Tailwind CSS配置
├── tsconfig.json           # TypeScript配置
├── package.json            # 项目依赖和脚本
└── README.md               # 项目说明文档
```

### 目录说明

#### `/src/components/`
存放可复用的React组件，每个组件应该：
- 有明确的单一职责
- 包含完整的TypeScript类型定义
- 遵循命名约定（PascalCase）
- 包含详细的JSDoc注释

#### `/src/pages/`
存放页面级组件，通常与路由一一对应：
- 页面组件负责布局和数据获取
- 可以组合多个components中的组件
- 处理页面级的状态管理

#### `/src/hooks/`
存放自定义Hook函数：
- 封装可复用的状态逻辑
- 遵循Hook命名约定（use开头）
- 提供清晰的类型定义和文档

#### `/src/lib/`
存放工具函数和配置：
- 纯函数工具类
- API客户端配置
- 常量定义
- 第三方库的封装

## 🛣️ 创建新页面和路由

### 🎯 核心概念理解

**重要**：在React单页应用中，路由路径是在代码中配置的，不是由文件名决定的！

- 📁 **文件名**：`src/pages/UserProfile.tsx`
- 🌐 **访问路径**：可以是 `/user`, `/profile`, `/dashboard` 等任意路径
- ⚙️ **配置位置**：在 `src/App.tsx` 中的 `<Route>` 组件定义

### 📋 完整创建流程

#### 步骤1：规划页面信息

在开始编码前，先明确以下信息：

```
页面名称：用户资料页面
文件名：UserProfile.tsx
访问路径：/user-profile
功能描述：显示和编辑用户个人信息
```

#### 步骤2：创建页面组件文件

在 `src/pages/` 目录下创建页面组件：

```typescript
// src/pages/UserProfile.tsx
import React from 'react';
import { UserProfileComponent } from '@/components/UserProfileComponent';

/**
 * @功能概述: 用户资料页面的页面级组件
 * @路由路径: /user-profile
 * @页面功能: 显示和编辑用户个人信息
 * @组件组合: 使用UserProfileComponent作为主要内容
 */
const UserProfile: React.FC = () => {
    return (
        <div className="min-h-screen bg-background">
            {/* 页面级布局和容器 */}
            <UserProfileComponent />
        </div>
    );
};

export default UserProfile;
```

#### 步骤3：创建功能组件文件

在 `src/components/` 目录下创建具体的功能组件：

```typescript
// src/components/UserProfileComponent.tsx
import React, { useState, useEffect } from 'react';

/**
 * @功能概述: 用户资料页面的主要功能组件
 * @状态管理: useState管理用户信息和编辑状态
 * @渲染逻辑: 条件渲染查看/编辑模式
 */
export const UserProfileComponent: React.FC = () => {
    const [userInfo, setUserInfo] = useState(null); // 用户信息状态
    const [isEditing, setIsEditing] = useState(false); // 编辑模式状态

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-3xl font-bold mb-6">用户资料</h1>

            {/* 用户信息显示区域 */}
            <div className="bg-white rounded-lg shadow p-6">
                {/* 具体的用户信息内容 */}
                <p>用户信息内容...</p>
            </div>
        </div>
    );
};
```

#### 步骤4：配置路由映射

**这是最关键的步骤！** 在 `src/App.tsx` 中添加路由配置：

```typescript
// src/App.tsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Index from './pages/Index';
import English from './pages/English';
import UserProfile from './pages/UserProfile'; // 1. 导入新页面组件

/**
 * @功能概述: 应用主组件，配置路由和全局布局
 * @路由配置: 定义所有页面的路由路径
 */
function App() {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/english" element={<English />} />
                {/* 2. 添加新路由配置 */}
                <Route path="/user-profile" element={<UserProfile />} />
            </Routes>
        </Router>
    );
}

export default App;
```

#### 步骤5：测试页面访问

1. **启动开发服务器**：
   ```bash
   npm run dev
   ```

2. **浏览器访问**：
   ```
   http://localhost:8080/user-profile
   ```

3. **验证页面显示**：确认页面正常加载和显示

#### 步骤6：添加导航链接（可选）

在其他页面或组件中添加导航链接：

```typescript
import { Link } from 'react-router-dom';

// 方法1：使用Link组件
<Link
    to="/user-profile"
    className="text-blue-600 hover:text-blue-800 underline"
>
    查看用户资料
</Link>

// 方法2：使用useNavigate Hook
import { useNavigate } from 'react-router-dom';

const navigate = useNavigate();
const handleGoToProfile = () => {
    navigate('/user-profile');
};

<button onClick={handleGoToProfile} className="btn btn-primary">
    前往用户资料
</button>
```

### 路由配置最佳实践

1. **路径命名规范**：
   - 使用小写字母和连字符：`/user-profile`
   - 避免驼峰命名：避免 `/userProfile`
   - 保持简洁明了：`/about` 而不是 `/about-us-page`

2. **动态路由**：
   ```typescript
   // 带参数的路由
   <Route path="/user/:id" element={<UserProfile />} />

   // 在组件中获取参数
   import { useParams } from 'react-router-dom';

   const UserProfile = () => {
       const { id } = useParams<{ id: string }>();
       return <div>用户ID: {id}</div>;
   };
   ```

3. **嵌套路由**：
   ```typescript
   <Route path="/dashboard" element={<Dashboard />}>
       <Route path="profile" element={<Profile />} />
       <Route path="settings" element={<Settings />} />
   </Route>
   ```

4. **路由守卫**：
   ```typescript
   // 创建受保护的路由组件
   const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
       const isAuthenticated = useAuth(); // 自定义认证Hook

       if (!isAuthenticated) {
           return <Navigate to="/login" replace />;
       }

       return <>{children}</>;
   };

   // 在路由中使用
   <Route
       path="/dashboard"
       element={
           <ProtectedRoute>
               <Dashboard />
           </ProtectedRoute>
       }
   />
   ```

### 路由访问方式

创建完成后，可以通过以下方式访问新页面：

1. **直接访问**：在浏览器地址栏输入 `http://localhost:8080/user-profile`
2. **程序导航**：使用 `useNavigate` Hook
   ```typescript
   import { useNavigate } from 'react-router-dom';

   const navigate = useNavigate();
   navigate('/user-profile');
   ```
3. **链接导航**：使用 `Link` 组件（如上所示）

### 🔍 路由定义机制详解

**关键理解**：路由路径完全由代码配置决定，与文件名无关！

#### 路由定义位置
- **文件**：`src/App.tsx`
- **具体行**：`<Route path="/路径" element={<组件 />} />`

#### 路径与文件名的关系
```typescript
// 示例：文件名与路径可以完全不同
<Route path="/learn" element={<English />} />        // 文件：English.tsx，路径：/learn
<Route path="/news" element={<BBCNewsOpening />} />  // 文件：BBCNewsOpening.tsx，路径：/news
<Route path="/study" element={<EnglishLearning />} />// 文件：EnglishLearning.tsx，路径：/study
```

#### 修改路由路径
如果想修改现有页面的访问路径，只需修改 `App.tsx` 中的 `path` 属性：

```typescript
// 原来：访问 /english
<Route path="/english" element={<English />} />

// 修改为：访问 /learn
<Route path="/learn" element={<English />} />

// 修改为：访问 /study-english
<Route path="/study-english" element={<English />} />
```

**注意**：修改路径后，原来的路径将无法访问，需要更新所有相关的导航链接。

## 🧩 组件开发规范

### 组件命名约定

1. **文件命名**：使用PascalCase，如 `UserProfile.tsx`
2. **组件名称**：与文件名保持一致
3. **导出方式**：页面组件使用默认导出，功能组件可使用命名导出

```typescript
// 页面组件 - 默认导出
const UserProfile: React.FC = () => { ... };
export default UserProfile;

// 功能组件 - 命名导出
export const UserProfileForm: React.FC = () => { ... };
```

### 组件结构模板

```typescript
import React, { useState, useEffect } from 'react';
import { ComponentProps } from '@/types';

/**
 * @功能概述: [组件的主要功能描述]
 * @props: [主要props说明]
 * @状态管理: [状态管理方式]
 * @副作用: [useEffect等副作用说明]
 */
interface Props {
    title: string;
    onSubmit?: (data: any) => void;
    className?: string;
}

export const MyComponent: React.FC<Props> = ({
    title,
    onSubmit,
    className = ''
}) => {
    // 1. 状态定义
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState(null);

    // 2. 副作用
    useEffect(() => {
        // 组件挂载时的逻辑
    }, []);

    // 3. 事件处理函数
    const handleSubmit = async () => {
        setLoading(true);
        try {
            // 处理逻辑
            onSubmit?.(data);
        } catch (error) {
            console.error('提交失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 4. 渲染逻辑
    return (
        <div className={`component-container ${className}`}>
            <h2 className="text-xl font-semibold mb-4">{title}</h2>
            {/* 组件内容 */}
        </div>
    );
};
```

### Props类型定义

```typescript
// 基础Props接口
interface BaseComponentProps {
    className?: string;
    children?: React.ReactNode;
}

// 扩展Props接口
interface UserCardProps extends BaseComponentProps {
    user: {
        id: string;
        name: string;
        email: string;
    };
    onEdit?: (userId: string) => void;
    onDelete?: (userId: string) => void;
}

// 可选Props使用
interface OptionalProps {
    title?: string;
    description?: string;
    isVisible?: boolean;
}
```

## 🎨 样式开发指南

### Tailwind CSS 使用规范

1. **优先使用Tailwind类**：
   ```typescript
   // ✅ 推荐
   <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow">

   // ❌ 避免
   <div style={{ display: 'flex', padding: '16px' }}>
   ```

2. **响应式设计**：
   ```typescript
   <div className="w-full md:w-1/2 lg:w-1/3 xl:w-1/4">
       {/* 响应式宽度 */}
   </div>
   ```

3. **条件样式**：
   ```typescript
   <button
       className={`
           px-4 py-2 rounded font-medium
           ${isActive ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}
           ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}
       `}
   >
       按钮文本
   </button>
   ```

### shadcn/ui 组件使用

1. **安装新组件**：
   ```bash
   pnpm dlx shadcn@latest add button
   pnpm dlx shadcn@latest add input
   pnpm dlx shadcn@latest add dialog
   ```

2. **组件导入和使用**：
   ```typescript
   import { Button } from '@/components/ui/button';
   import { Input } from '@/components/ui/input';
   import { Dialog, DialogContent, DialogHeader } from '@/components/ui/dialog';

   export const MyForm = () => {
       return (
           <div className="space-y-4">
               <Input placeholder="输入内容" />
               <Button variant="default" size="lg">
                   提交
               </Button>
           </div>
       );
   };
   ```

3. **自定义样式**：
   ```typescript
   // 扩展shadcn/ui组件样式
   <Button
       variant="outline"
       className="border-blue-500 text-blue-500 hover:bg-blue-50"
   >
       自定义按钮
   </Button>
   ```

### 自定义CSS（当Tailwind不够用时）

在 `src/index.css` 中添加自定义样式：

```css
/* 全局样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
    .btn-primary {
        @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors;
    }

    .card {
        @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
    }
}

/* 自定义工具类 */
@layer utilities {
    .text-gradient {
        background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}
```

## 🎨 页面特效修改通用规则

### ⚠️ 重要警告：绝对不要修改构建后的文件！

**错误做法**：
- ❌ **绝对不要修改** `dist/index.html` 或 `dist/` 目录下的任何文件
- ❌ **绝对不要修改** 构建后的 `.js` 和 `.css` 文件
- ❌ **绝对不要直接编辑** 浏览器中看到的HTML源码

**为什么不能修改构建后的文件**：
1. **文件会被覆盖**：每次运行 `npm run build` 都会重新生成 `dist/` 目录
2. **代码无法维护**：构建后的代码是压缩和混淆的，无法阅读和维护
3. **团队协作问题**：其他开发者无法看到你的修改，会造成严重的团队协作问题
4. **版本控制混乱**：构建文件不应该提交到Git，修改会导致版本控制混乱

**正确做法**：
- ✅ **只修改源代码**：`src/` 目录下的 `.tsx`、`.ts`、`.css` 文件
- ✅ **通过构建系统**：修改源码后让Vite自动重新构建
- ✅ **使用开发服务器**：在 `npm run dev` 模式下实时预览修改效果

---

### 📋 正确的四步修改流程

当客户对某个页面的特效（动画、样式、交互）不满意时，按照以下四步流程进行修改：

### 步骤1：确定页面对应的文件

**目标**：从访问URL找到对应的页面组件文件

**操作流程**：
1. **查看路由配置**：打开 `src/App.tsx` 文件
2. **匹配路径**：在 `<Routes>` 中找到对应的 `<Route path="..." element={...} />`
3. **确定页面组件**：记录 `element` 属性中的组件名称
4. **定位页面文件**：在 `src/pages/` 目录下找到对应的 `.tsx` 文件

**查看文件**：
- `src/App.tsx` - 路由配置文件
- `src/pages/[PageName].tsx` - 页面组件文件

### 步骤2：定位目标代码文件

**目标**：找到包含实际特效逻辑的组件文件

**操作流程**：
1. **打开页面文件**：查看 `src/pages/[PageName].tsx` 的内容
2. **查找导入组件**：找到 `import` 语句中的主要组件
3. **定位组件文件**：在 `src/components/` 目录下找到对应的 `.tsx` 文件
4. **确认目标文件**：这个文件包含所有的特效、动画和交互逻辑

**查看文件**：
- `src/pages/[PageName].tsx` - 页面入口文件
- `src/components/[ComponentName].tsx` - 实际特效代码文件

### 步骤3：理解代码结构

**目标**：分析组件的结构和特效实现方式

**分析要点**：
1. **组件注释**：阅读文件顶部的 JSDoc 注释，了解功能概述
2. **状态管理**：查看 `useState`、`useRef` 等 Hook 的使用
3. **特效函数**：找到处理动画、样式变化的函数
4. **渲染结构**：理解 JSX 的布局和样式类名
5. **交互逻辑**：查看事件处理函数（onClick、onHover 等）

**重点关注**：
- 动画相关：GSAP、CSS transitions、keyframes
- 样式相关：className、style 属性、CSS 变量
- 交互相关：事件处理函数、状态变化逻辑

### 步骤4：修改特效的具体位置

**目标**：精确定位并修改特效代码

**修改策略**：
1. **备份文件**：修改前先复制一份原文件
2. **确保开发服务器运行**：执行 `npm run dev` 启动开发服务器
3. **小步修改**：每次只修改一个特效，避免大范围改动
4. **实时预览**：保存后立即在浏览器中查看效果
5. **逐步调试**：如果效果不理想，逐步调整参数

**常见修改位置**：
- **颜色修改**：查找 `text-[color]`、`bg-[color]`、`border-[color]` 类名
- **动画修改**：查找 `transition`、`duration`、`ease` 相关属性
- **布局修改**：查找 `flex`、`grid`、`absolute`、`relative` 等布局类名
- **尺寸修改**：查找 `w-`、`h-`、`p-`、`m-` 等尺寸类名
- **交互修改**：查找 `hover:`、`focus:`、`active:` 等状态类名

**修改文件**：
- `src/components/[ComponentName].tsx` - 主要修改文件

### 步骤5：测试修改效果

**测试流程**：
1. **保存文件**：修改完成后保存源代码文件
2. **自动重新加载**：Vite开发服务器会自动检测文件变化并重新编译
3. **浏览器预览**：在浏览器中查看 `http://localhost:8080/[页面路径]`
4. **验证效果**：确认修改是否达到预期效果
5. **多设备测试**：在不同屏幕尺寸下测试响应式效果

**测试检查清单**：
- ✅ 特效是否按预期工作
- ✅ 没有破坏其他页面功能
- ✅ 在不同浏览器中正常显示
- ✅ 移动端适配正常
- ✅ 性能没有明显下降

**如果效果不理想**：
1. **检查控制台错误**：打开浏览器开发者工具查看错误信息
2. **回滚修改**：如果出现严重问题，恢复备份文件
3. **逐步调试**：减小修改范围，逐个测试
4. **查看文档**：参考Tailwind CSS或相关库的文档

**最终确认**：
- 客户确认效果满意后，可以考虑构建生产版本：`npm run build`
- 但日常开发和测试都应该在开发模式下进行

---

## 📝 实例：修改 `/english` 页面特效

### 实例背景
客户对 `http://localhost:8080/english` 页面的特效不满意，需要修改文字高亮效果和布局样式。

### 步骤1：确定页面对应的文件

1. **查看路由配置**：
   ```typescript
   // src/App.tsx 第20行
   <Route path="/english" element={<English />} />
   ```

2. **确定页面组件**：`English` 组件

3. **定位页面文件**：`src/pages/English.tsx`

### 步骤2：定位目标代码文件

1. **打开页面文件**：
   ```typescript
   // src/pages/English.tsx
   import { EnglishLearningPage } from '@/components/EnglishLearningPage';

   const English = () => {
       return <EnglishLearningPage />;
   };
   ```

2. **确定目标文件**：`src/components/EnglishLearningPage.tsx`

### 步骤3：理解代码结构

**组件功能**：英语学习页面，三栏布局展示新闻字幕和重点词汇

**关键结构**：
- **状态管理**：`useState` 管理悬停单词状态
- **数据配置**：`currentSubtitle` 和 `vocabularyList`
- **特效函数**：`renderSubtitleWithHighlights` 处理文字高亮
- **布局结构**：左侧杂志封面、中间字幕、右侧词汇列表

### 步骤4：修改特效的具体位置

**常见修改需求和对应位置**：

1. **修改文字高亮颜色**：
   ```typescript
   // 第58-61行
   const colorClass =
     highlightedWord.type === 'emphasis' ? 'text-orange-400' :     // 强调词颜色
     highlightedWord.type === 'key' ? 'text-orange-300' :         // 关键词颜色
     highlightedWord.type === 'vocabulary' ? 'text-green-400' :   // 词汇词颜色
     'text-white';                                                // 默认颜色
   ```

2. **修改词汇卡片样式**：
   ```typescript
   // 第168-184行
   <div className="bg-gray-700/70 rounded-xl p-5 border border-gray-600/50 hover:bg-gray-700 transition-colors">
   ```

3. **修改布局比例**：
   ```typescript
   // 第98行：左侧区域宽度
   <div className="w-1/4 bg-gray-800">

   // 第132行：中间区域宽度
   <div className="w-1/2 bg-gray-900">

   // 第158行：右侧区域宽度
   <div className="w-1/4 bg-gray-800">
   ```

4. **修改字体大小**：
   ```typescript
   // 第148行：字幕文字大小
   <p className="text-3xl leading-relaxed font-medium text-white">
   ```

**修改示例**：
如果客户想要更鲜艳的高亮颜色，可以修改：
```typescript
// 原来
highlightedWord.type === 'vocabulary' ? 'text-green-400' :

// 修改为
highlightedWord.type === 'vocabulary' ? 'text-emerald-500' :
```

保存文件后，浏览器会自动刷新显示新效果。




## 🔄 状态管理

### React状态管理最佳实践

#### 本地状态管理 (useState)

**适用场景**：组件内部的简单状态

```typescript
import React, { useState } from 'react';

/**
 * @功能概述: 用户信息表单组件
 * @状态管理: useState管理表单数据和验证状态
 */
const UserForm: React.FC = () => {
    // 表单数据状态
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        age: 0
    });

    // 验证状态
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    // 处理输入变化
    const handleInputChange = (field: string, value: string | number) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // 清除对应字段的错误
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    return (
        <form>
            <input
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="姓名"
            />
            {errors.name && <span className="text-red-500">{errors.name}</span>}
        </form>
    );
};
```



#### 复杂状态管理 (useReducer)

**适用场景**：复杂的状态逻辑，多个相关状态

```typescript
import React, { useReducer } from 'react';

// 状态类型定义
interface AppState {
    user: User | null;
    loading: boolean;
    error: string | null;
    theme: 'light' | 'dark';
}

// 动作类型定义
type AppAction =
    | { type: 'SET_LOADING'; payload: boolean }
    | { type: 'SET_USER'; payload: User }
    | { type: 'SET_ERROR'; payload: string }
    | { type: 'TOGGLE_THEME' };

// Reducer函数
const appReducer = (state: AppState, action: AppAction): AppState => {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, loading: action.payload };
        case 'SET_USER':
            return { ...state, user: action.payload, loading: false, error: null };
        case 'SET_ERROR':
            return { ...state, error: action.payload, loading: false };
        case 'TOGGLE_THEME':
            return { ...state, theme: state.theme === 'light' ? 'dark' : 'light' };
        default:
            return state;
    }
};




/**
 * @功能概述: 应用主状态管理组件
 * @状态管理: useReducer管理复杂的应用状态
 */
const App: React.FC = () => {
    const [state, dispatch] = useReducer(appReducer, {
        user: null,
        loading: false,
        error: null,
        theme: 'light'
    });

    return (
        <div className={`app ${state.theme}`}>
            {/* 应用内容 */}
        </div>
    );
};
```

#### 全局状态管理 (Context API)

**适用场景**：跨组件共享状态

```typescript
import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// 创建Context
const AppContext = createContext<{
    state: AppState;
    dispatch: React.Dispatch<AppAction>;
} | null>(null);

/**
 * @功能概述: 全局状态提供者组件
 * @状态管理: Context API + useReducer提供全局状态
 */
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [state, dispatch] = useReducer(appReducer, initialState);

    return (
        <AppContext.Provider value={{ state, dispatch }}>
            {children}
        </AppContext.Provider>
    );
};

/**
 * @功能概述: 自定义Hook，用于访问全局状态
 * @返回值: { state, dispatch } - 全局状态和派发函数
 */
export const useAppContext = () => {
    const context = useContext(AppContext);
    if (!context) {
        throw new Error('useAppContext must be used within AppProvider');
    }
    return context;
};

// 在组件中使用
const UserProfile: React.FC = () => {
    const { state, dispatch } = useAppContext();

    return (
        <div>
            <h1>欢迎, {state.user?.name}</h1>
            <button onClick={() => dispatch({ type: 'TOGGLE_THEME' })}>
                切换主题
            </button>
        </div>
    );
};
```

#### 状态管理选择指南

1. **useState** - 简单的本地状态
   - 表单输入
   - 开关状态
   - 计数器

2. **useReducer** - 复杂的本地状态
   - 多个相关状态
   - 复杂的状态更新逻辑
   - 状态机模式

3. **Context API** - 全局状态
   - 用户认证信息
   - 主题设置
   - 语言设置

4. **第三方库** (可选)
   - **Zustand** - 轻量级状态管理
   - **Redux Toolkit** - 复杂应用状态管理
   - **Jotai** - 原子化状态管理

## 🌐 API集成

### 自定义Hook进行API调用

**推荐方式**：创建可复用的API调用Hook

```typescript
// src/hooks/useApiCall.ts
import { useState, useEffect, useCallback } from 'react';

interface ApiCallResult<T> {
    data: T | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
}

/**
 * @功能概述: 通用API调用Hook
 * @参数说明: url - API端点, options - 请求配置
 * @返回值: { data, loading, error, refetch }
 * @使用示例: const { data, loading } = useApiCall('/api/users')
 */
export const useApiCall = <T>(url: string, options?: RequestInit): ApiCallResult<T> => {
    const [data, setData] = useState<T | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchData = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options?.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const result = await response.json();
            setData(result);
        } catch (err) {
            setError(err instanceof Error ? err.message : '未知错误');
        } finally {
            setLoading(false);
        }
    }, [url, options]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    return { data, loading, error, refetch: fetchData };
};
```

### 在组件中使用API

```typescript
// src/components/UserList.tsx
import React from 'react';
import { useApiCall } from '@/hooks/useApiCall';

interface User {
    id: number;
    name: string;
    email: string;
}

/**
 * @功能概述: 用户列表组件
 * @状态管理: useApiCall管理API调用状态
 * @渲染逻辑: 条件渲染加载、错误、数据状态
 */
export const UserList: React.FC = () => {
    const { data: users, loading, error, refetch } = useApiCall<User[]>('/api/users');

    if (loading) {
        return <div className="text-center p-4">加载中...</div>;
    }

    if (error) {
        return (
            <div className="text-red-500 p-4">
                <p>错误: {error}</p>
                <button
                    onClick={refetch}
                    className="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
                >
                    重试
                </button>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {users?.map(user => (
                <div key={user.id} className="p-4 border rounded">
                    <h3 className="font-semibold">{user.name}</h3>
                    <p className="text-gray-600">{user.email}</p>
                </div>
            ))}
        </div>
    );
};
```

### POST请求示例

```typescript
// src/hooks/useCreateUser.ts
import { useState } from 'react';

interface CreateUserData {
    name: string;
    email: string;
}

/**
 * @功能概述: 创建用户的API调用Hook
 * @返回值: { createUser, loading, error, success }
 */
export const useCreateUser = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const createUser = async (userData: CreateUserData) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        try {
            const response = await fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                throw new Error(`创建失败: ${response.status}`);
            }

            setSuccess(true);
        } catch (err) {
            setError(err instanceof Error ? err.message : '创建失败');
        } finally {
            setLoading(false);
        }
    };

    return { createUser, loading, error, success };
};
```

### API配置和拦截器

```typescript
// src/lib/api.ts
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

/**
 * @功能概述: API请求封装函数
 * @参数说明: endpoint - API端点, options - 请求配置
 * @返回值: Promise<Response>
 */
export const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers,
        },
        ...options,
    };

    // 添加认证token（如果存在）
    const token = localStorage.getItem('authToken');
    if (token) {
        config.headers = {
            ...config.headers,
            'Authorization': `Bearer ${token}`,
        };
    }

    const response = await fetch(url, config);

    // 统一错误处理
    if (!response.ok) {
        if (response.status === 401) {
            // 处理认证失败
            localStorage.removeItem('authToken');
            window.location.href = '/login';
        }
        throw new Error(`API错误: ${response.status}`);
    }

    return response;
};
```

## 🧪 测试指南

### 测试环境设置

**安装测试依赖**：
```bash
# 安装测试相关依赖
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event
npm install --save-dev vitest jsdom
```

**配置测试环境**：
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
  },
});
```

```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
```

### 组件单元测试

**基础组件测试**：
```typescript
// src/components/__tests__/UserCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { UserCard } from '../UserCard';

/**
 * @功能概述: UserCard组件单元测试
 * @测试范围: 渲染、交互、props传递
 */
describe('UserCard', () => {
    const mockUser = {
        id: '1',
        name: '张三',
        email: '<EMAIL>'
    };

    const mockOnEdit = vi.fn();
    const mockOnDelete = vi.fn();

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('应该正确渲染用户信息', () => {
        render(
            <UserCard
                user={mockUser}
                onEdit={mockOnEdit}
                onDelete={mockOnDelete}
            />
        );

        expect(screen.getByText('张三')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('应该在点击编辑按钮时调用onEdit', () => {
        render(
            <UserCard
                user={mockUser}
                onEdit={mockOnEdit}
                onDelete={mockOnDelete}
            />
        );

        const editButton = screen.getByRole('button', { name: /编辑/i });
        fireEvent.click(editButton);

        expect(mockOnEdit).toHaveBeenCalledWith('1');
    });

    it('应该在点击删除按钮时调用onDelete', () => {
        render(
            <UserCard
                user={mockUser}
                onEdit={mockOnEdit}
                onDelete={mockOnDelete}
            />
        );

        const deleteButton = screen.getByRole('button', { name: /删除/i });
        fireEvent.click(deleteButton);

        expect(mockOnDelete).toHaveBeenCalledWith('1');
    });
});
```

### Hook测试

**自定义Hook测试**：
```typescript
// src/hooks/__tests__/useApiCall.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { useApiCall } from '../useApiCall';

// Mock fetch
global.fetch = vi.fn();

/**
 * @功能概述: useApiCall Hook单元测试
 * @测试范围: 数据获取、错误处理、重新获取
 */
describe('useApiCall', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('应该成功获取数据', async () => {
        const mockData = { id: 1, name: '测试用户' };

        (fetch as vi.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => mockData,
        });

        const { result } = renderHook(() => useApiCall('/api/users'));

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.data).toEqual(mockData);
        expect(result.current.error).toBeNull();
    });

    it('应该处理API错误', async () => {
        (fetch as vi.Mock).mockResolvedValueOnce({
            ok: false,
            status: 404,
        });

        const { result } = renderHook(() => useApiCall('/api/users'));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.data).toBeNull();
        expect(result.current.error).toBe('HTTP错误: 404');
    });
});
```

### 集成测试

**页面级集成测试**：
```typescript
// src/pages/__tests__/UserProfile.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import UserProfile from '../UserProfile';

// Mock API调用
vi.mock('@/hooks/useApiCall', () => ({
    useApiCall: vi.fn(),
}));

/**
 * @功能概述: UserProfile页面集成测试
 * @测试范围: 页面渲染、数据加载、用户交互
 */
describe('UserProfile Page', () => {
    const renderWithRouter = (component: React.ReactElement) => {
        return render(
            <BrowserRouter>
                {component}
            </BrowserRouter>
        );
    };

    it('应该显示加载状态', () => {
        const mockUseApiCall = vi.mocked(useApiCall);
        mockUseApiCall.mockReturnValue({
            data: null,
            loading: true,
            error: null,
            refetch: vi.fn(),
        });

        renderWithRouter(<UserProfile />);

        expect(screen.getByText('加载中...')).toBeInTheDocument();
    });

    it('应该显示用户数据', async () => {
        const mockUserData = {
            id: '1',
            name: '张三',
            email: '<EMAIL>'
        };

        const mockUseApiCall = vi.mocked(useApiCall);
        mockUseApiCall.mockReturnValue({
            data: mockUserData,
            loading: false,
            error: null,
            refetch: vi.fn(),
        });

        renderWithRouter(<UserProfile />);

        await waitFor(() => {
            expect(screen.getByText('张三')).toBeInTheDocument();
            expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        });
    });
});
```

### 测试最佳实践

1. **测试命名规范**：
   - 使用描述性的测试名称
   - 遵循 "应该...当...时" 的格式
   - 使用中文描述，便于理解

2. **测试组织**：
   - 每个组件/Hook一个测试文件
   - 使用 `describe` 分组相关测试
   - 使用 `beforeEach` 清理状态

3. **Mock策略**：
   - Mock外部依赖（API、第三方库）
   - 使用 `vi.fn()` 创建mock函数
   - 验证函数调用和参数

4. **测试覆盖率**：
   ```bash
   # 运行测试并生成覆盖率报告
   npm run test -- --coverage
   ```

5. **测试脚本**：
   ```json
   // package.json
   {
     "scripts": {
       "test": "vitest",
       "test:ui": "vitest --ui",
       "test:coverage": "vitest --coverage"
     }
   }
   ```

## 🏗️ 构建和部署

### 开发环境 vs 生产环境

**开发环境特点**：
- 使用 `npm run dev` 启动
- 热模块替换 (HMR) 支持
- 源码映射 (Source Maps) 便于调试
- 未压缩的代码，便于阅读
- 实时错误提示和类型检查

**生产环境特点**：
- 使用 `npm run build` 构建
- 代码压缩和混淆
- 资源优化和合并
- 移除开发工具和调试信息
- 最小化文件大小

### 构建流程

**基础构建命令**：
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 清理构建缓存（如果需要）
rm -rf dist node_modules/.vite
npm install
npm run build
```

**构建配置优化**：
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false, // 生产环境关闭源码映射
    minify: 'terser', // 使用terser压缩
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
        },
      },
    },
  },
  server: {
    port: 8080,
    host: true, // 允许外部访问
  },
});
```

### 部署方案

#### 方案1：静态文件托管

**适用场景**：纯前端应用，无后端API依赖

**部署步骤**：
```bash
# 1. 构建项目
npm run build

# 2. 上传dist目录到静态托管服务
# - Vercel: 直接连接Git仓库自动部署
# - Netlify: 拖拽dist文件夹或Git集成
# - GitHub Pages: 使用GitHub Actions自动部署
# - 阿里云OSS/腾讯云COS: 上传到对象存储
```

**Vercel部署配置**：
```json
// vercel.json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

**Netlify部署配置**：
```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### 方案2：Docker容器化部署

**适用场景**：需要自定义服务器环境或与后端服务集成

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # SPA路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 环境变量配置

**开发环境变量**：
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_TITLE=开发环境
VITE_ENABLE_MOCK=true
```

**生产环境变量**：
```bash
# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=生产环境
VITE_ENABLE_MOCK=false
```

**在代码中使用**：
```typescript
// src/lib/config.ts
export const config = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  appTitle: import.meta.env.VITE_APP_TITLE,
  enableMock: import.meta.env.VITE_ENABLE_MOCK === 'true',
};
```

### 性能优化

**代码分割**：
```typescript
// 路由级代码分割
import { lazy, Suspense } from 'react';

const English = lazy(() => import('./pages/English'));
const UserProfile = lazy(() => import('./pages/UserProfile'));

function App() {
  return (
    <Router>
      <Suspense fallback={<div>加载中...</div>}>
        <Routes>
          <Route path="/english" element={<English />} />
          <Route path="/user-profile" element={<UserProfile />} />
        </Routes>
      </Suspense>
    </Router>
  );
}
```

## 📏 代码规范

### TypeScript规范

#### 类型定义

**接口命名**：
```typescript
// ✅ 推荐：使用PascalCase，描述性命名
interface UserProfile {
    id: string;
    name: string;
    email: string;
    createdAt: Date;
}

// ✅ 推荐：Props接口以Props结尾
interface UserCardProps {
    user: UserProfile;
    onEdit?: (id: string) => void;
    onDelete?: (id: string) => void;
}

// ❌ 避免：使用I前缀
interface IUser { ... }
```

**类型别名**：
```typescript
// ✅ 推荐：联合类型使用type
type Theme = 'light' | 'dark' | 'auto';
type Status = 'loading' | 'success' | 'error';

// ✅ 推荐：复杂类型组合
type ApiResponse<T> = {
    data: T;
    status: number;
    message: string;
};
```

**泛型使用**：
```typescript
// ✅ 推荐：有意义的泛型参数名
interface Repository<TEntity, TKey = string> {
    findById(id: TKey): Promise<TEntity | null>;
    save(entity: TEntity): Promise<TEntity>;
    delete(id: TKey): Promise<void>;
}

// ✅ 推荐：约束泛型
interface Identifiable {
    id: string;
}

function updateEntity<T extends Identifiable>(entity: T): T {
    return { ...entity, updatedAt: new Date() };
}
```

#### 函数规范

**函数声明**：
```typescript
// ✅ 推荐：明确的返回类型
const fetchUser = async (id: string): Promise<UserProfile | null> => {
    try {
        const response = await apiRequest(`/users/${id}`);
        return await response.json();
    } catch (error) {
        console.error('获取用户失败:', error);
        return null;
    }
};

// ✅ 推荐：使用类型守卫
const isValidEmail = (email: string): email is string => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};
```

### React规范

#### 组件定义

**函数组件**：
```typescript
// ✅ 推荐：使用React.FC类型
interface ButtonProps {
    variant?: 'primary' | 'secondary' | 'danger';
    size?: 'sm' | 'md' | 'lg';
    disabled?: boolean;
    children: React.ReactNode;
    onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({
    variant = 'primary',
    size = 'md',
    disabled = false,
    children,
    onClick
}) => {
    return (
        <button
            className={`btn btn-${variant} btn-${size}`}
            disabled={disabled}
            onClick={onClick}
        >
            {children}
        </button>
    );
};

export default Button;
```

**Hook使用规范**：
```typescript
// ✅ 推荐：自定义Hook命名以use开头
const useLocalStorage = <T>(key: string, initialValue: T) => {
    const [storedValue, setStoredValue] = useState<T>(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            console.error('读取localStorage失败:', error);
            return initialValue;
        }
    });

    const setValue = (value: T | ((val: T) => T)) => {
        try {
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            setStoredValue(valueToStore);
            window.localStorage.setItem(key, JSON.stringify(valueToStore));
        } catch (error) {
            console.error('写入localStorage失败:', error);
        }
    };

    return [storedValue, setValue] as const;
};
```

#### 事件处理

**事件处理函数命名**：
```typescript
// ✅ 推荐：使用handle前缀
const UserForm: React.FC = () => {
    const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        // 处理表单提交
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target;
        // 处理输入变化
    };

    const handleButtonClick = () => {
        // 处理按钮点击
    };

    return (
        <form onSubmit={handleSubmit}>
            <input onChange={handleInputChange} />
            <button onClick={handleButtonClick}>提交</button>
        </form>
    );
};
```

### 样式规范

#### Tailwind CSS规范

**类名组织**：
```typescript
// ✅ 推荐：按功能分组类名
<div className={`
    // 布局
    flex items-center justify-between
    // 尺寸
    w-full h-12 px-4 py-2
    // 外观
    bg-white border border-gray-200 rounded-lg shadow-sm
    // 交互
    hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500
    // 响应式
    md:w-auto lg:px-6
`}>
```

**条件样式**：
```typescript
// ✅ 推荐：使用clsx或cn工具函数
import { clsx } from 'clsx';

const Button: React.FC<ButtonProps> = ({ variant, size, disabled, className }) => {
    return (
        <button
            className={clsx(
                // 基础样式
                'inline-flex items-center justify-center font-medium rounded-md transition-colors',
                // 变体样式
                {
                    'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
                    'bg-gray-200 text-gray-900 hover:bg-gray-300': variant === 'secondary',
                    'bg-red-600 text-white hover:bg-red-700': variant === 'danger',
                },
                // 尺寸样式
                {
                    'px-3 py-1.5 text-sm': size === 'sm',
                    'px-4 py-2 text-base': size === 'md',
                    'px-6 py-3 text-lg': size === 'lg',
                },
                // 状态样式
                {
                    'opacity-50 cursor-not-allowed': disabled,
                },
                // 自定义样式
                className
            )}
            disabled={disabled}
        >
            {children}
        </button>
    );
};
```

### 文件组织规范

#### 导入顺序

```typescript
// 1. React相关导入
import React, { useState, useEffect, useCallback } from 'react';

// 2. 第三方库导入
import { clsx } from 'clsx';
import { format } from 'date-fns';

// 3. 内部组件导入
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// 4. 相对路径导入
import { UserCard } from './UserCard';
import { useUserData } from '../hooks/useUserData';

// 5. 类型导入（放在最后）
import type { User, UserProfile } from '@/types';
```

#### 导出规范

```typescript
// ✅ 推荐：页面组件使用默认导出
const UserProfile: React.FC = () => {
    // 组件实现
};

export default UserProfile;

// ✅ 推荐：工具组件使用命名导出
export const UserCard: React.FC<UserCardProps> = ({ user }) => {
    // 组件实现
};

export const UserList: React.FC<UserListProps> = ({ users }) => {
    // 组件实现
};

// ✅ 推荐：类型定义使用命名导出
export interface UserCardProps {
    user: User;
    onEdit?: (id: string) => void;
}

export type UserStatus = 'active' | 'inactive' | 'pending';
```

### 注释规范

**遵循项目注释标准**：
- 参考 `code-commenting-standard.md` 文档
- 使用JSDoc格式的块注释
- 为所有公开的函数、组件、Hook添加注释
- 使用中文注释，便于团队理解

```typescript
/**
 * @功能概述: 用户资料卡片组件
 * @props: user - 用户信息对象, onEdit - 编辑回调函数
 * @状态管理: 无本地状态，纯展示组件
 * @渲染逻辑: 展示用户基本信息和操作按钮
 */
const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
    // 组件实现
};
```

## 🔧 故障排除

### 常见问题

#### 开发服务器问题

**问题：开发服务器启动失败**
```bash
# 解决方案1：清除node_modules并重新安装
rm -rf node_modules package-lock.json
npm install

# 解决方案2：清除Vite缓存
rm -rf node_modules/.vite
npm run dev

# 解决方案3：检查端口占用
netstat -ano | findstr :8080  # Windows
lsof -i :8080                 # macOS/Linux
```

**问题：热重载不工作**
```typescript
// 检查vite.config.ts配置
export default defineConfig({
  server: {
    host: true,
    port: 8080,
    watch: {
      usePolling: true, // 在某些环境下需要启用轮询
    },
  },
});
```

**问题：模块解析错误**
```typescript
// 检查路径别名配置
export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
});
```

#### TypeScript类型错误

**问题：类型检查失败**
```bash
# 重新生成类型定义
npm run type-check

# 清除TypeScript缓存
rm -rf node_modules/.cache
npx tsc --build --clean
```

**问题：第三方库类型缺失**
```bash
# 安装类型定义
npm install --save-dev @types/react @types/react-dom
npm install --save-dev @types/node

# 检查tsconfig.json配置
{
  "compilerOptions": {
    "types": ["vite/client", "@testing-library/jest-dom"]
  }
}
```

#### 样式问题

**问题：Tailwind CSS样式不生效**
```typescript
// 检查tailwind.config.js配置
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

```css
/* 检查src/index.css导入 */
@tailwind base;
@tailwind components;
@tailwind utilities;
```

**问题：shadcn/ui组件样式异常**
```bash
# 重新安装shadcn/ui组件
pnpm dlx shadcn@latest add button --overwrite

# 检查components.json配置
{
  "style": "default",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/index.css"
  }
}
```

#### 构建问题

**问题：构建失败**
```bash
# 检查构建错误详情
npm run build -- --verbose

# 清理并重新构建
rm -rf dist
npm run build

# 检查内存限制
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

**问题：构建后路由404**
```nginx
# 确保服务器配置支持SPA路由
location / {
    try_files $uri $uri/ /index.html;
}
```

#### 性能问题

**问题：页面加载缓慢**
```typescript
// 启用代码分割
const LazyComponent = lazy(() => import('./HeavyComponent'));

// 使用Suspense包装
<Suspense fallback={<Loading />}>
  <LazyComponent />
</Suspense>
```

**问题：内存泄漏**
```typescript
// 清理副作用
useEffect(() => {
  const timer = setInterval(() => {
    // 定时器逻辑
  }, 1000);

  return () => {
    clearInterval(timer); // 清理定时器
  };
}, []);

// 清理事件监听器
useEffect(() => {
  const handleResize = () => {
    // 处理逻辑
  };

  window.addEventListener('resize', handleResize);

  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);
```

### 调试技巧

#### 浏览器开发者工具

**React DevTools**：
```bash
# 安装React DevTools浏览器扩展
# Chrome: React Developer Tools
# Firefox: React Developer Tools
```

**性能分析**：
```typescript
// 使用React Profiler
import { Profiler } from 'react';

const onRenderCallback = (id, phase, actualDuration) => {
  console.log('组件渲染时间:', { id, phase, actualDuration });
};

<Profiler id="UserList" onRender={onRenderCallback}>
  <UserList />
</Profiler>
```

#### 日志调试

**开发环境日志**：
```typescript
// 使用console.group组织日志
console.group('用户数据处理');
console.log('原始数据:', rawData);
console.log('处理后数据:', processedData);
console.groupEnd();

// 使用console.table显示表格数据
console.table(users);

// 使用console.time测量性能
console.time('数据处理');
processData();
console.timeEnd('数据处理');
```

**生产环境错误监控**：
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);
  // 发送错误到监控服务
});

// React错误边界
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>出现了错误</h1>;
    }

    return this.props.children;
  }
}
```

### 性能优化建议

1. **代码分割** - 使用 React.lazy() 进行路由级别的代码分割
2. **图片优化** - 使用适当的图片格式和尺寸
3. **依赖分析** - 定期检查和清理未使用的依赖
4. **构建分析** - 使用 `npm run build` 查看构建大小

```bash
# 分析构建包大小
npm install --save-dev rollup-plugin-visualizer
npm run build -- --analyze
```

### 团队协作问题

**Git冲突解决**：
```bash
# 拉取最新代码
git pull origin main

# 解决冲突后
git add .
git commit -m "解决合并冲突"
git push origin feature-branch
```

**代码格式化**：
```bash
# 安装Prettier
npm install --save-dev prettier

# 格式化所有文件
npx prettier --write "src/**/*.{js,jsx,ts,tsx}"
```

**ESLint配置**：
```json
// .eslintrc.json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/no-unused-vars": "error"
  }
}
```
