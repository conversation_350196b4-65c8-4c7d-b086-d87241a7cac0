---
type: "always_apply"
description: "React/TypeScript项目架构指南"
globs: ["**/*.tsx", "**/*.ts", "**/*.jsx", "**/*.js"]
alwaysApply: true
---

# React/TypeScript 项目架构指南

## 📋 目录

1. [项目概述](#项目概述)
2. [技术栈架构](#技术栈架构)
3. [项目结构详解](#项目结构详解)
4. [架构设计原则](#架构设计原则)
5. [数据流架构](#数据流架构)
6. [文件组织规范](#文件组织规范)

---

## 🎯 项目概述

本项目是一个基于React和TypeScript的现代化单页应用(SPA)，使用Vite作为构建工具，shadcn/ui作为UI组件库，Tailwind CSS作为样式框架。项目主要功能包括BBC新闻开场动画展示和英语学习页面。

### 项目特点
- **单页应用 (SPA)** - 所有页面在同一个HTML文档中渲染
- **组件化架构** - 基于React组件的模块化开发
- **类型安全** - 全面使用TypeScript进行类型检查
- **现代化工具链** - Vite + ESBuild 提供极速开发体验
- **响应式设计** - 支持多设备和屏幕尺寸

---

## 🛠️ 技术栈架构

### 核心技术层
```
┌─────────────────────────────────────────┐
│                前端框架                  │
│  React 18 + TypeScript + React Router   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                UI层                     │
│     shadcn/ui + Tailwind CSS           │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              构建工具层                  │
│        Vite + ESBuild + Rollup         │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              开发工具层                  │
│    ESLint + Prettier + TypeScript      │
└─────────────────────────────────────────┘
```

### 技术选型说明

- **Vite** - 极速构建工具，支持热重载和TypeScript
- **React 18** - 现代化组件开发，函数式组件和Hooks
- **TypeScript** - 类型安全，减少运行时错误
- **shadcn/ui** - 现代化UI组件库，基于Radix UI
- **Tailwind CSS** - 原子化CSS框架，快速构建界面

---

## 📁 项目结构详解

### 完整目录结构

```
news-reel-opener-magic/
├── src/                    # 源代码目录
│   ├── components/         # React组件
│   │   ├── BBCNewsOpening.tsx      # BBC新闻开场动画组件
│   │   └── EnglishLearningPage.tsx # 英语学习页面组件
│   ├── pages/              # 页面组件
│   │   ├── Index.tsx       # 首页组件
│   │   └── English.tsx     # 英语学习页面
│   ├── hooks/              # 自定义Hook (可选)
│   │   └── useApiCall.ts   # API调用Hook示例
│   ├── lib/                # 工具函数和配置
│   │   └── utils.ts        # 通用工具函数
│   ├── types/              # TypeScript类型定义
│   │   └── index.ts        # 全局类型定义
│   ├── assets/             # 静态资源
│   │   └── classroom-background.jpg # 背景图片
│   ├── App.tsx             # 主应用组件和路由配置
│   ├── main.tsx            # 应用入口点
│   └── index.css           # 全局样式和Tailwind导入
├── public/                 # 公共静态文件
│   ├── index.html          # HTML模板
│   └── favicon.ico         # 网站图标
├── dist/                   # 构建输出目录
├── .augment/               # Augment配置目录
│   └── rules/              # 开发规范文档
├── vite.config.ts          # Vite配置文件
├── tailwind.config.js      # Tailwind CSS配置
├── tsconfig.json           # TypeScript配置
├── package.json            # 项目依赖和脚本
└── README.md               # 项目说明文档
```

### 核心目录详解

#### `/src/` - 源代码根目录
所有应用源代码的存放位置，包含组件、页面、工具函数等。

#### `/src/components/` - 可复用组件
存放可复用的React组件，每个组件应该：
- **单一职责原则** - 每个组件只负责一个功能
- **完整类型定义** - 包含完整的TypeScript接口
- **命名约定** - 使用PascalCase命名（如 `UserProfile.tsx`）
- **详细注释** - 包含JSDoc格式的功能说明

**组件分类**：
- **UI组件** - 纯展示组件，如按钮、卡片、表单元素
- **业务组件** - 包含业务逻辑的功能组件
- **布局组件** - 页面布局和容器组件

#### `/src/pages/` - 页面级组件
存放页面级组件，通常与路由一一对应：
- **页面容器** - 负责页面整体布局和数据获取
- **组件组合** - 组合多个components中的组件
- **路由对应** - 每个页面对应一个路由路径
- **状态管理** - 处理页面级的状态管理

#### `/src/hooks/` - 自定义Hook
存放自定义Hook函数：
- **状态逻辑封装** - 封装可复用的状态逻辑
- **命名约定** - 遵循Hook命名约定（use开头）
- **类型定义** - 提供清晰的类型定义和文档
- **副作用管理** - 处理API调用、事件监听等副作用

#### `/src/lib/` - 工具函数和配置
存放工具函数和配置：
- **纯函数工具** - 不依赖外部状态的工具函数
- **API客户端** - HTTP请求封装和配置
- **常量定义** - 应用常量和配置项
- **第三方库封装** - 对第三方库的封装和适配

#### `/src/types/` - TypeScript类型定义
存放全局类型定义：
- **接口定义** - 数据结构接口
- **类型别名** - 联合类型和复杂类型
- **枚举定义** - 常量枚举
- **泛型类型** - 可复用的泛型定义

#### `/src/assets/` - 静态资源
存放静态资源文件：
- **图片资源** - PNG、JPG、SVG等图片文件
- **字体文件** - 自定义字体资源
- **图标资源** - 应用图标和装饰性图标
- **多媒体** - 音频、视频等媒体文件

### 配置文件说明

#### `vite.config.ts` - Vite配置
- **构建配置** - 生产环境构建优化
- **开发服务器** - 热重载和代理配置
- **插件配置** - React、TypeScript等插件
- **路径别名** - `@` 指向 `src` 目录

#### `tailwind.config.js` - Tailwind配置
- **内容扫描** - 指定需要扫描的文件
- **主题定制** - 自定义颜色、字体、间距等
- **插件配置** - 第三方Tailwind插件
- **响应式断点** - 自定义响应式断点

#### `tsconfig.json` - TypeScript配置
- **编译选项** - TypeScript编译器配置
- **路径映射** - 模块路径解析配置
- **类型检查** - 严格模式和类型检查规则
- **包含/排除** - 指定编译包含的文件

## 🏗️ 架构设计原则

### 单一职责原则 (SRP)
每个组件、函数、模块都应该只有一个明确的职责

### 开放封闭原则 (OCP)
组件应该对扩展开放，对修改封闭

### 组件组合优于继承
React推荐使用组合而不是继承来构建组件

### 依赖倒置原则 (DIP)
高层模块不应该依赖低层模块，两者都应该依赖抽象

---

## 🔄 数据流架构

### 单向数据流
React遵循单向数据流原则，数据从父组件流向子组件：

```
┌─────────────────┐
│   App Component │ ← 应用根组件
└─────────┬───────┘
          │ props
          ▼
┌─────────────────┐
│  Page Component │ ← 页面级组件
└─────────┬───────┘
          │ props
          ▼
┌─────────────────┐
│ Feature Component│ ← 功能组件
└─────────┬───────┘
          │ props
          ▼
┌─────────────────┐
│  UI Component   │ ← UI组件
└─────────────────┘
```

### 状态管理层次

1. **本地状态 (Local State)** - 使用 `useState` 管理组件内部状态
2. **提升状态 (Lifted State)** - 将状态提升到共同的父组件
3. **全局状态 (Global State)** - 使用 Context API 管理全局状态

## 📂 文件组织规范

### 导入顺序规范

```typescript
// 1. React相关导入
import React, { useState, useEffect, useCallback } from 'react';

// 2. 第三方库导入
import { clsx } from 'clsx';
import { format } from 'date-fns';

// 3. 内部组件导入
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// 4. 相对路径导入
import { UserCard } from './UserCard';
import { useUserData } from '../hooks/useUserData';

// 5. 类型导入（放在最后）
import type { User, UserProfile } from '@/types';
```

### 文件命名规范

- **组件文件**: PascalCase (如 `UserProfile.tsx`)
- **Hook文件**: camelCase with use prefix (如 `useApiCall.ts`)
- **工具文件**: camelCase (如 `formatUtils.ts`)
- **类型文件**: camelCase (如 `userTypes.ts`)
- **常量文件**: camelCase (如 `apiConstants.ts`)

### 目录结构最佳实践

```
src/
├── components/
│   ├── ui/              # 基础UI组件
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── index.ts     # 统一导出
│   ├── forms/           # 表单相关组件
│   │   ├── UserForm.tsx
│   │   └── LoginForm.tsx
│   └── layout/          # 布局组件
│       ├── Header.tsx
│       └── Sidebar.tsx
├── pages/
│   ├── auth/            # 认证相关页面
│   │   ├── Login.tsx
│   │   └── Register.tsx
│   └── dashboard/       # 仪表板页面
│       └── Dashboard.tsx
├── hooks/
│   ├── api/             # API相关Hook
│   │   ├── useUsers.ts
│   │   └── useAuth.ts
│   └── ui/              # UI相关Hook
│       └── useModal.ts
├── lib/
│   ├── api/             # API客户端
│   │   ├── client.ts
│   │   └── endpoints.ts
│   ├── utils/           # 工具函数
│   │   ├── format.ts
│   │   └── validation.ts
│   └── constants/       # 常量定义
│       └── app.ts
└── types/
    ├── api.ts           # API类型
    ├── user.ts          # 用户类型
    └── index.ts         # 统一导出
```

### 模块导出规范

```typescript
// 统一导出文件 (index.ts)
export { Button } from './Button';
export { Input } from './Input';
export { Card } from './Card';

// 类型统一导出
export type { ButtonProps } from './Button';
export type { InputProps } from './Input';
export type { CardProps } from './Card';
```

### 数据获取模式

#### 1. 组件级数据获取
在组件内部获取数据，适用于简单场景

#### 2. 自定义Hook数据获取
封装数据获取逻辑，提高复用性

### 性能优化架构

#### 1. 代码分割
使用React.lazy进行路由级代码分割

#### 2. 组件优化
使用React.memo和useMemo优化组件性能

---

**总结**：良好的项目架构是可维护、可扩展代码的基础。遵循这些架构原则和规范，可以帮助团队构建高质量的React/TypeScript应用。通过合理的文件组织、清晰的数据流设计和性能优化策略，可以确保项目在长期发展中保持良好的可维护性和扩展性。
