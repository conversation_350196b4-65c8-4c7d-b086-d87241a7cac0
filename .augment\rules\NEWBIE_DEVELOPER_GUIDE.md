---
type: "always_apply"
description: "新手程序员核心开发指南"
globs: ["**/*.tsx", "**/*.ts", "**/*.jsx", "**/*.js"]
alwaysApply: true
---

# 新手程序员核心开发指南

## 📋 目录

1. [🛣️ 创建新页面和路由](#创建新页面和路由)
2. [🎨 页面特效修改通用规则](#页面特效修改通用规则)
3. [🧩 组件开发规范](#组件开发规范)
4. [🎨 样式开发指南](#样式开发指南)
5. [📝 实例：修改 `/english` 页面特效](#实例修改-english-页面特效)

---

## 🛣️ 创建新页面和路由

### 🎯 核心概念理解

**重要**：在React单页应用中，路由路径是在代码中配置的，不是由文件名决定的！

- 📁 **文件名**：`src/pages/UserProfile.tsx` 
- 🌐 **访问路径**：可以是 `/user`, `/profile`, `/dashboard` 等任意路径
- ⚙️ **配置位置**：在 `src/App.tsx` 中的 `<Route>` 组件定义

### 📋 完整创建流程

#### 步骤1：规划页面信息

在开始编码前，先明确以下信息：

```
页面名称：用户资料页面
文件名：UserProfile.tsx
访问路径：/user-profile
功能描述：显示和编辑用户个人信息
```

#### 步骤2：创建页面组件文件

在 `src/pages/` 目录下创建页面组件：

```typescript
// src/pages/UserProfile.tsx
import React from 'react';
import { UserProfileComponent } from '@/components/UserProfileComponent';

/**
 * @功能概述: 用户资料页面的页面级组件
 * @路由路径: /user-profile
 * @页面功能: 显示和编辑用户个人信息
 * @组件组合: 使用UserProfileComponent作为主要内容
 */
const UserProfile: React.FC = () => {
    return (
        <div className="min-h-screen bg-background">
            {/* 页面级布局和容器 */}
            <UserProfileComponent />
        </div>
    );
};

export default UserProfile;
```

#### 步骤3：创建功能组件文件

在 `src/components/` 目录下创建具体的功能组件：

```typescript
// src/components/UserProfileComponent.tsx
import React, { useState, useEffect } from 'react';

/**
 * @功能概述: 用户资料页面的主要功能组件
 * @状态管理: useState管理用户信息和编辑状态
 * @渲染逻辑: 条件渲染查看/编辑模式
 */
export const UserProfileComponent: React.FC = () => {
    const [userInfo, setUserInfo] = useState(null); // 用户信息状态
    const [isEditing, setIsEditing] = useState(false); // 编辑模式状态

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-3xl font-bold mb-6">用户资料</h1>
            
            {/* 用户信息显示区域 */}
            <div className="bg-white rounded-lg shadow p-6">
                {/* 具体的用户信息内容 */}
                <p>用户信息内容...</p>
            </div>
        </div>
    );
};
```

#### 步骤4：配置路由映射

**这是最关键的步骤！** 在 `src/App.tsx` 中添加路由配置：

```typescript
// src/App.tsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Index from './pages/Index';
import English from './pages/English';
import UserProfile from './pages/UserProfile'; // 1. 导入新页面组件

/**
 * @功能概述: 应用主组件，配置路由和全局布局
 * @路由配置: 定义所有页面的路由路径
 */
function App() {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/english" element={<English />} />
                {/* 2. 添加新路由配置 */}
                <Route path="/user-profile" element={<UserProfile />} />
            </Routes>
        </Router>
    );
}

export default App;
```

#### 步骤5：测试页面访问

1. **启动开发服务器**：
   ```bash
   npm run dev
   ```

2. **浏览器访问**：
   ```
   http://localhost:8080/user-profile
   ```

3. **验证页面显示**：确认页面正常加载和显示

#### 步骤6：添加导航链接（可选）

在其他页面或组件中添加导航链接：

```typescript
import { Link } from 'react-router-dom';

// 方法1：使用Link组件
<Link 
    to="/user-profile" 
    className="text-blue-600 hover:text-blue-800 underline"
>
    查看用户资料
</Link>

// 方法2：使用useNavigate Hook
import { useNavigate } from 'react-router-dom';

const navigate = useNavigate();
const handleGoToProfile = () => {
    navigate('/user-profile');
};

<button onClick={handleGoToProfile} className="btn btn-primary">
    前往用户资料
</button>
```

### 🔍 路由定义机制详解

**关键理解**：路由路径完全由代码配置决定，与文件名无关！

#### 路由定义位置
- **文件**：`src/App.tsx`
- **具体行**：`<Route path="/路径" element={<组件 />} />`

#### 路径与文件名的关系
```typescript
// 示例：文件名与路径可以完全不同
<Route path="/learn" element={<English />} />        // 文件：English.tsx，路径：/learn
<Route path="/news" element={<BBCNewsOpening />} />  // 文件：BBCNewsOpening.tsx，路径：/news
<Route path="/study" element={<EnglishLearning />} />// 文件：EnglishLearning.tsx，路径：/study
```

#### 修改路由路径
如果想修改现有页面的访问路径，只需修改 `App.tsx` 中的 `path` 属性：

```typescript
// 原来：访问 /english
<Route path="/english" element={<English />} />

// 修改为：访问 /learn
<Route path="/learn" element={<English />} />

// 修改为：访问 /study-english  
<Route path="/study-english" element={<English />} />
```

**注意**：修改路径后，原来的路径将无法访问，需要更新所有相关的导航链接。

---

## 🎨 页面特效修改通用规则

### ⚠️ 重要警告：绝对不要修改构建后的文件！

**错误做法**：
- ❌ **绝对不要修改** `dist/index.html` 或 `dist/` 目录下的任何文件
- ❌ **绝对不要修改** 构建后的 `.js` 和 `.css` 文件
- ❌ **绝对不要直接编辑** 浏览器中看到的HTML源码

**为什么不能修改构建后的文件**：
1. **文件会被覆盖**：每次运行 `npm run build` 都会重新生成 `dist/` 目录
2. **代码无法维护**：构建后的代码是压缩和混淆的，无法阅读和维护
3. **团队协作问题**：其他开发者无法看到你的修改，会造成严重的团队协作问题
4. **版本控制混乱**：构建文件不应该提交到Git，修改会导致版本控制混乱

**正确做法**：
- ✅ **只修改源代码**：`src/` 目录下的 `.tsx`、`.ts`、`.css` 文件
- ✅ **通过构建系统**：修改源码后让Vite自动重新构建
- ✅ **使用开发服务器**：在 `npm run dev` 模式下实时预览修改效果

---

### 📋 正确的四步修改流程

当客户对某个页面的特效（动画、样式、交互）不满意时，按照以下四步流程进行修改：

### 步骤1：确定页面对应的文件

**目标**：从访问URL找到对应的页面组件文件

**操作流程**：
1. **查看路由配置**：打开 `src/App.tsx` 文件
2. **匹配路径**：在 `<Routes>` 中找到对应的 `<Route path="..." element={...} />`
3. **确定页面组件**：记录 `element` 属性中的组件名称
4. **定位页面文件**：在 `src/pages/` 目录下找到对应的 `.tsx` 文件

**查看文件**：
- `src/App.tsx` - 路由配置文件
- `src/pages/[PageName].tsx` - 页面组件文件

### 步骤2：定位目标代码文件

**目标**：找到包含实际特效逻辑的组件文件

**操作流程**：
1. **打开页面文件**：查看 `src/pages/[PageName].tsx` 的内容
2. **查找导入组件**：找到 `import` 语句中的主要组件
3. **定位组件文件**：在 `src/components/` 目录下找到对应的 `.tsx` 文件
4. **确认目标文件**：这个文件包含所有的特效、动画和交互逻辑

**查看文件**：
- `src/pages/[PageName].tsx` - 页面入口文件
- `src/components/[ComponentName].tsx` - 实际特效代码文件

### 步骤3：理解代码结构

**目标**：分析组件的结构和特效实现方式

**分析要点**：
1. **组件注释**：阅读文件顶部的 JSDoc 注释，了解功能概述
2. **状态管理**：查看 `useState`、`useRef` 等 Hook 的使用
3. **特效函数**：找到处理动画、样式变化的函数
4. **渲染结构**：理解 JSX 的布局和样式类名
5. **交互逻辑**：查看事件处理函数（onClick、onHover 等）

**重点关注**：
- 动画相关：GSAP、CSS transitions、keyframes
- 样式相关：className、style 属性、CSS 变量
- 交互相关：事件处理函数、状态变化逻辑

### 步骤4：修改特效的具体位置

**目标**：精确定位并修改特效代码

**修改策略**：
1. **备份文件**：修改前先复制一份原文件
2. **确保开发服务器运行**：执行 `npm run dev` 启动开发服务器
3. **小步修改**：每次只修改一个特效，避免大范围改动
4. **实时预览**：保存后立即在浏览器中查看效果
5. **逐步调试**：如果效果不理想，逐步调整参数

**常见修改位置**：
- **颜色修改**：查找 `text-[color]`、`bg-[color]`、`border-[color]` 类名
- **动画修改**：查找 `transition`、`duration`、`ease` 相关属性
- **布局修改**：查找 `flex`、`grid`、`absolute`、`relative` 等布局类名
- **尺寸修改**：查找 `w-`、`h-`、`p-`、`m-` 等尺寸类名
- **交互修改**：查找 `hover:`、`focus:`、`active:` 等状态类名

**修改文件**：
- `src/components/[ComponentName].tsx` - 主要修改文件

### 步骤5：测试修改效果

**测试流程**：
1. **保存文件**：修改完成后保存源代码文件
2. **自动重新加载**：Vite开发服务器会自动检测文件变化并重新编译
3. **浏览器预览**：在浏览器中查看 `http://localhost:8080/[页面路径]`
4. **验证效果**：确认修改是否达到预期效果
5. **多设备测试**：在不同屏幕尺寸下测试响应式效果

**测试检查清单**：
- ✅ 特效是否按预期工作
- ✅ 没有破坏其他页面功能
- ✅ 在不同浏览器中正常显示
- ✅ 移动端适配正常
- ✅ 性能没有明显下降

**如果效果不理想**：
1. **检查控制台错误**：打开浏览器开发者工具查看错误信息
2. **回滚修改**：如果出现严重问题，恢复备份文件
3. **逐步调试**：减小修改范围，逐个测试
4. **查看文档**：参考Tailwind CSS或相关库的文档

**最终确认**：
- 客户确认效果满意后，可以考虑构建生产版本：`npm run build`
- 但日常开发和测试都应该在开发模式下进行

## 🧩 组件开发规范

### 组件命名约定

1. **文件命名**：使用PascalCase，如 `UserProfile.tsx`
2. **组件名称**：与文件名保持一致
3. **导出方式**：页面组件使用默认导出，功能组件可使用命名导出

```typescript
// 页面组件 - 默认导出
const UserProfile: React.FC = () => { ... };
export default UserProfile;

// 功能组件 - 命名导出
export const UserProfileForm: React.FC = () => { ... };
```

### 组件结构模板

```typescript
import React, { useState, useEffect } from 'react';
import { ComponentProps } from '@/types';

/**
 * @功能概述: [组件的主要功能描述]
 * @props: [主要props说明]
 * @状态管理: [状态管理方式]
 * @副作用: [useEffect等副作用说明]
 */
interface Props {
    title: string;
    onSubmit?: (data: any) => void;
    className?: string;
}

export const MyComponent: React.FC<Props> = ({
    title,
    onSubmit,
    className = ''
}) => {
    // 1. 状态定义
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState(null);

    // 2. 副作用
    useEffect(() => {
        // 组件挂载时的逻辑
    }, []);

    // 3. 事件处理函数
    const handleSubmit = async () => {
        setLoading(true);
        try {
            // 处理逻辑
            onSubmit?.(data);
        } catch (error) {
            console.error('提交失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 4. 渲染逻辑
    return (
        <div className={`component-container ${className}`}>
            <h2 className="text-xl font-semibold mb-4">{title}</h2>
            {/* 组件内容 */}
        </div>
    );
};
```

### Props类型定义

```typescript
// 基础Props接口
interface BaseComponentProps {
    className?: string;
    children?: React.ReactNode;
}

// 扩展Props接口
interface UserCardProps extends BaseComponentProps {
    user: {
        id: string;
        name: string;
        email: string;
    };
    onEdit?: (userId: string) => void;
    onDelete?: (userId: string) => void;
}

// 可选Props使用
interface OptionalProps {
    title?: string;
    description?: string;
    isVisible?: boolean;
}
```

---

## 🎨 样式开发指南

### Tailwind CSS 使用规范

1. **优先使用Tailwind类**：
   ```typescript
   // ✅ 推荐
   <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow">

   // ❌ 避免
   <div style={{ display: 'flex', padding: '16px' }}>
   ```

2. **响应式设计**：
   ```typescript
   <div className="w-full md:w-1/2 lg:w-1/3 xl:w-1/4">
       {/* 响应式宽度 */}
   </div>
   ```

3. **条件样式**：
   ```typescript
   <button
       className={`
           px-4 py-2 rounded font-medium
           ${isActive ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}
           ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}
       `}
   >
       按钮文本
   </button>
   ```

### shadcn/ui 组件使用

1. **安装新组件**：
   ```bash
   pnpm dlx shadcn@latest add button
   pnpm dlx shadcn@latest add input
   pnpm dlx shadcn@latest add dialog
   ```

2. **组件导入和使用**：
   ```typescript
   import { Button } from '@/components/ui/button';
   import { Input } from '@/components/ui/input';
   import { Dialog, DialogContent, DialogHeader } from '@/components/ui/dialog';

   export const MyForm = () => {
       return (
           <div className="space-y-4">
               <Input placeholder="输入内容" />
               <Button variant="default" size="lg">
                   提交
               </Button>
           </div>
       );
   };
   ```

3. **自定义样式**：
   ```typescript
   // 扩展shadcn/ui组件样式
   <Button
       variant="outline"
       className="border-blue-500 text-blue-500 hover:bg-blue-50"
   >
       自定义按钮
   </Button>
   ```

### 自定义CSS（当Tailwind不够用时）

在 `src/index.css` 中添加自定义样式：

```css
/* 全局样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
    .btn-primary {
        @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors;
    }

    .card {
        @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
    }
}

/* 自定义工具类 */
@layer utilities {
    .text-gradient {
        background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}
```

## 📝 实例：修改 `/english` 页面特效

### 实例背景
客户对 `http://localhost:8080/english` 页面的特效不满意，需要修改文字高亮效果和布局样式。

### 步骤1：确定页面对应的文件

1. **查看路由配置**：
   ```typescript
   // src/App.tsx 第20行
   <Route path="/english" element={<English />} />
   ```

2. **确定页面组件**：`English` 组件

3. **定位页面文件**：`src/pages/English.tsx`

### 步骤2：定位目标代码文件

1. **打开页面文件**：
   ```typescript
   // src/pages/English.tsx
   import { EnglishLearningPage } from '@/components/EnglishLearningPage';

   const English = () => {
       return <EnglishLearningPage />;
   };
   ```

2. **确定目标文件**：`src/components/EnglishLearningPage.tsx`

### 步骤3：理解代码结构

**组件功能**：英语学习页面，三栏布局展示新闻字幕和重点词汇

**关键结构**：
- **状态管理**：`useState` 管理悬停单词状态
- **数据配置**：`currentSubtitle` 和 `vocabularyList`
- **特效函数**：`renderSubtitleWithHighlights` 处理文字高亮
- **布局结构**：左侧杂志封面、中间字幕、右侧词汇列表

### 步骤4：修改特效的具体位置

**常见修改需求和对应位置**：

1. **修改文字高亮颜色**：
   ```typescript
   // 第58-61行
   const colorClass =
     highlightedWord.type === 'emphasis' ? 'text-orange-400' :     // 强调词颜色
     highlightedWord.type === 'key' ? 'text-orange-300' :         // 关键词颜色
     highlightedWord.type === 'vocabulary' ? 'text-green-400' :   // 词汇词颜色
     'text-white';                                                // 默认颜色
   ```

2. **修改词汇卡片样式**：
   ```typescript
   // 第168-184行
   <div className="bg-gray-700/70 rounded-xl p-5 border border-gray-600/50 hover:bg-gray-700 transition-colors">
   ```

3. **修改布局比例**：
   ```typescript
   // 第98行：左侧区域宽度
   <div className="w-1/4 bg-gray-800">

   // 第132行：中间区域宽度
   <div className="w-1/2 bg-gray-900">

   // 第158行：右侧区域宽度
   <div className="w-1/4 bg-gray-800">
   ```

4. **修改字体大小**：
   ```typescript
   // 第148行：字幕文字大小
   <p className="text-3xl leading-relaxed font-medium text-white">
   ```

**修改示例**：
如果客户想要更鲜艳的高亮颜色，可以修改：
```typescript
// 原来
highlightedWord.type === 'vocabulary' ? 'text-green-400' :

// 修改为
highlightedWord.type === 'vocabulary' ? 'text-emerald-500' :
```

保存文件后，浏览器会自动刷新显示新效果。

### 步骤5：测试修改效果

**测试流程**：
1. **保存文件**：修改完成后保存源代码文件
2. **自动重新加载**：Vite开发服务器会自动检测文件变化并重新编译
3. **浏览器预览**：在浏览器中查看 `http://localhost:8080/english`
4. **验证效果**：确认修改是否达到预期效果

**常见修改效果**：
- 文字颜色变化立即可见
- 布局调整实时反映
- 动画效果即时更新
- 响应式变化可通过调整浏览器窗口大小测试

---

## 🎯 新手开发者快速上手清单

### 必备技能检查

- [ ] 理解React组件的基本结构
- [ ] 掌握TypeScript基础语法
- [ ] 熟悉Tailwind CSS类名使用
- [ ] 了解路由配置原理
- [ ] 会使用浏览器开发者工具

### 常用命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 安装新的shadcn/ui组件
pnpm dlx shadcn@latest add [component-name]

# 类型检查
npm run type-check
```

### 重要文件位置

- **路由配置**：`src/App.tsx`
- **页面组件**：`src/pages/`
- **功能组件**：`src/components/`
- **样式文件**：`src/index.css`
- **类型定义**：`src/types/`

### 调试技巧

1. **查看控制台错误**：F12 → Console
2. **检查元素样式**：F12 → Elements
3. **网络请求调试**：F12 → Network
4. **React组件调试**：安装React DevTools扩展

### 求助资源

- **项目文档**：`.augment/rules/DEVELOP_GUIDELINES.md`
- **注释标准**：`.augment/rules/code-commenting-standard.md`
- **Tailwind CSS文档**：https://tailwindcss.com/docs
- **React文档**：https://react.dev/
- **TypeScript文档**：https://www.typescriptlang.org/docs/

---

**记住**：遇到问题时，先查看控制台错误信息，然后参考相关文档，最后再寻求帮助。保持耐心，多练习，你会很快掌握这些技能的！
