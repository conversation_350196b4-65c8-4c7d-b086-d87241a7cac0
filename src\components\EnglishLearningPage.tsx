import { useState } from 'react';

/**
 * @功能概述: 英语学习页面组件，展示新闻字幕和重点词汇
 * @状态管理: useState管理悬停单词状态
 * @渲染逻辑: 三栏布局 - 杂志封面、主要字幕、词汇列表
 */
const EnglishLearningPage = () => {
  const [hoveredWord, setHoveredWord] = useState<string | null>(null); // 当前悬停的单词状态

  // 当前字幕数据，包含高亮单词配置
  const currentSubtitle = {
    text: "The European Union said that it would be virtually impossible for its current volume of trade with America to continue", // 字幕文本
    highlightedWords: [ // 高亮单词配置数组
      { word: "The", type: "normal", definition: "" }, // 普通单词
      { word: "European", type: "emphasis", definition: "" }, // 强调单词
      { word: "Union", type: "emphasis", definition: "" }, // 强调单词
      { word: "said", type: "key", definition: "" }, // 关键单词
      { word: "that", type: "normal", definition: "" }, // 普通单词
      { word: "it", type: "normal", definition: "" }, // 普通单词
      { word: "would", type: "key", definition: "" }, // 关键单词
      { word: "be", type: "normal", definition: "" }, // 普通单词
      { word: "virtually", type: "vocabulary", definition: "" }, // 词汇单词
      { word: "impossible", type: "key", definition: "" }, // 关键单词
      { word: "for", type: "normal", definition: "" }, // 普通单词
      { word: "its", type: "normal", definition: "" }, // 普通单词
      { word: "current", type: "key", definition: "" }, // 关键单词
      { word: "volume", type: "key", definition: "" }, // 关键单词
      { word: "of", type: "normal", definition: "" }, // 普通单词
      { word: "trade", type: "normal", definition: "" }, // 普通单词
      { word: "with", type: "normal", definition: "" }, // 普通单词
      { word: "America", type: "normal", definition: "" }, // 普通单词
      { word: "to", type: "normal", definition: "" }, // 普通单词
      { word: "continue", type: "normal", definition: "" } // 普通单词
    ]
  };

  // 重点词汇定义列表
  const vocabularyList = [
    { word: "virtually", phonetic: "/ˈvɜːtʃuəli/", pos: "adv.", meaning: "几乎；虚拟地" }, // 词汇1
    { word: "tit-for-tat", phonetic: "/tɪt fər ˈtæt/", pos: "adj.", meaning: "针锋相对的" }, // 词汇2
    { word: "bourbon", phonetic: "/ˈbɜːrbən/", pos: "n.", meaning: "波旁威士忌" }, // 词汇3
    { word: "levy", phonetic: "/ˈlevi/", pos: "v.", meaning: "征收（税）n. 税款；征兵" } // 词汇4
  ];

  /**
   * @功能概述: 渲染带有高亮效果的字幕文本
   * @param text 原始字幕文本
   * @param highlightedWords 高亮单词配置数组
   * @returns 带有颜色高亮的JSX元素
   * @执行流程: 分割文本 → 匹配高亮配置 → 应用颜色样式 → 渲染元素
   */
  const renderSubtitleWithHighlights = (text: string, highlightedWords: any[]) => {
    return (
      <>
        {text.split(' ').map((word, index) => { // 按空格分割文本为单词数组
          const cleanWord = word.replace(/[.,!?]/g, ''); // 移除标点符号，获取纯单词
          const highlightedWord = highlightedWords.find(hw => hw.word === cleanWord); // 查找匹配的高亮配置

          // 如果找到高亮配置，应用相应的颜色样式
          if (highlightedWord) {
            const colorClass =
              highlightedWord.type === 'emphasis' ? 'text-orange-400' : // 强调单词 - 橙色400
              highlightedWord.type === 'key' ? 'text-orange-300' : // 关键单词 - 橙色300
              highlightedWord.type === 'vocabulary' ? 'text-green-400' : // 词汇单词 - 绿色400
              'text-white'; // 默认白色

            return (
              <span key={index}>
                <span className={`${colorClass} font-semibold`}>
                  {word} {/* 渲染带颜色的单词 */}
                </span>
                {index < text.split(' ').length - 1 && ' '} {/* 添加单词间空格 */}
              </span>
            );
          }

          // 普通单词使用白色显示
          return (
            <span key={index} className="text-white">
              {word}
              {index < text.split(' ').length - 1 && ' '} {/* 添加单词间空格 */}
            </span>
          );
        })}
      </>
    );
  };

  return (
    <div className="w-full h-screen bg-black flex items-center justify-center">
      <div
        className="w-full h-full max-w-none bg-black"
        style={{ aspectRatio: '16/9', maxHeight: '100vh' }} // 设置16:9宽高比
      >
        <div className="h-full flex"> {/* 三栏布局容器 */}
          {/* 左侧区域 - 杂志封面 */}
          <div className="w-1/4 bg-gray-800 flex items-center justify-center p-8">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-sm">
              {/* 杂志标题 */}
              <div className="bg-red-600 text-white px-4 py-2 text-sm font-bold mb-4 rounded">
                The Economist
              </div>
              {/* 杂志副标题内容 */}
              <div className="text-xs text-gray-600 mb-2">
                Weekly look at the year
              </div>
              <div className="text-xs text-gray-600 mb-2">
                The great inflation
              </div>
              <div className="text-xs text-gray-600 mb-6">
                The rise and rise of women's sport
              </div>
              {/* 主标题 */}
              <div className="text-red-600 font-bold text-xl mb-3">
                Winning the war on
              </div>
              {/* 突出显示的关键词 */}
              <div
                className="text-red-600 font-black text-5xl leading-none tracking-tight"
                style={{
                  textShadow: '2px 2px 0px rgba(255,255,255,0.8)', // 文字阴影效果
                  WebkitTextStroke: '1px rgba(200,0,0,0.3)' // 文字描边效果
                }}
              >
                CHINA
              </div>
            </div>
          </div>

          {/* 中间区域 - 主要内容 */}
          <div className="w-1/2 bg-gray-900 flex flex-col justify-between p-8">
            {/* 头部信息 */}
            <div className="mb-8">
              <h1 className="text-red-500 text-5xl font-bold mb-4">
                The World This Week
              </h1>
              <p className="text-gray-300 text-2xl mb-2">
                Tariffs War, China GDP, Nvidia, UK VE {/* 新闻主题 */}
              </p>
              <p className="text-gray-400 text-lg">
                4.5 mins | July 17, 2025 {/* 时长和日期信息 */}
              </p>
            </div>

            {/* 主要字幕区域 - 焦点内容 */}
            <div className="flex-1 flex items-center">
              <div className="bg-black/50 rounded-xl p-8 border border-gray-600 w-full">
                <p className="text-3xl leading-relaxed font-medium text-white">
                  {renderSubtitleWithHighlights(currentSubtitle.text, currentSubtitle.highlightedWords)} {/* 渲染高亮字幕 */}
                </p>
              </div>
            </div>

            {/* 底部间距 */}
            <div className="h-16"></div>
          </div>

          {/* 右侧区域 - 词汇列表 */}
          <div className="w-1/4 bg-gray-800 p-6 overflow-y-auto">
            {/* 词汇区域标题 */}
            <div className="flex items-center mb-8">
              <div className="w-1 h-8 bg-red-500 mr-4 rounded"></div> {/* 装饰性红色竖线 */}
              <h3 className="text-red-400 text-xl font-bold">重点词汇</h3>
            </div>

            {/* 词汇卡片列表 */}
            <div className="space-y-6">
              {vocabularyList.map((vocab, index) => (
                <div
                  key={index}
                  className="bg-gray-700/70 rounded-xl p-5 border border-gray-600/50 hover:bg-gray-700 transition-colors" // 词汇卡片样式
                >
                  {/* 单词 */}
                  <div className="text-blue-300 font-semibold text-lg mb-2">
                    {vocab.word}
                  </div>
                  {/* 音标和词性 */}
                  <div className="text-gray-400 text-sm mb-3">
                    {vocab.phonetic} {vocab.pos}
                  </div>
                  {/* 中文释义 */}
                  <div className="text-gray-200 text-sm leading-relaxed">
                    {vocab.meaning}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnglishLearningPage;