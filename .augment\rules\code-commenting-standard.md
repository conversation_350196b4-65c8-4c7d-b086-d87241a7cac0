---
description: 
globs: 
alwaysApply: true
---
# React/TypeScript 代码注释标准

目标: 统一React/TypeScript代码库中的注释风格，提高代码的可读性和可维护性。

!!! 严格执行: AI 在编写或修改代码时，必须遵循以下注释标准添加注释。注释的目的是解释代码意图，而不是简单重复代码本身。

## 注释优先级策略

### 必须注释 (严格执行)
- 所有React组件 (函数组件和类组件)
- 所有自定义Hook函数
- 复杂的业务逻辑函数 (超过20行或包含3个以上分支)
- 关键的错误处理逻辑
- 与外部API交互的函数 (HTTP请求、第三方服务等)
- 路由配置和页面组件
- 状态管理相关函数 (Redux actions, reducers等)

### 推荐注释 (重要功能)
- 工具函数和辅助方法
- 数据处理和转换逻辑
- 事件处理函数
- 表单验证逻辑
- 配置文件和常量定义

### 可选注释 (简单逻辑)
- 简单的getter/setter方法
- 一目了然的UI渲染逻辑
- 标准的props传递

## 注释类型

主要使用两种类型的注释：块注释和行内注释。

### 1. 块注释 (Block Comments)

用于注释整个文件、类、函数、方法或复杂的代码块。

#### 基础结构要求

*   位置: 紧邻在被注释代码块的正上方。
*   语法: 使用TypeScript/JavaScript的标准块注释语法 (`/** ... */`)。
*   格式: 使用 JSDoc 风格，星号对齐，内部保持统一缩进。

#### 通用函数和方法注释模板

```typescript
/**
 * @功能概述: [1-3句话描述功能、目的和核心逻辑]
 * @param {类型} 参数名 - [参数用途和约束]
 * @returns {类型} [返回值描述或系统影响]
 * @执行流程: [可选，列出关键处理步骤]
 */
```

#### 项目特定注释模板

**React组件模板:**
```typescript
/**
 * @功能概述: [组件功能和用途描述]
 * @props: [主要props说明]
 * @状态管理: [使用的状态管理方式，如useState、useContext等]
 * @副作用: [useEffect等副作用说明]
 * @渲染逻辑: [关键渲染逻辑和条件]
 */
```

**自定义Hook模板:**
```typescript
/**
 * @功能概述: [Hook的功能和使用场景]
 * @参数说明: [输入参数的类型和用途]
 * @返回值: [返回的状态和方法说明]
 * @依赖项: [依赖的其他Hook或外部状态]
 * @使用示例: [简单的使用示例]
 */
```

**API交互函数模板:**
```typescript
/**
 * @功能概述: [API调用的具体用途]
 * @请求参数: [请求参数类型和格式]
 * @响应格式: [成功/失败响应结构]
 * @错误处理: [错误处理策略]
 * @缓存策略: [如果有缓存，说明缓存逻辑]
 */
```

**工具函数模板:**
```typescript
/**
 * @功能概述: [工具函数的具体功能]
 * @输入格式: [输入参数的类型和约束]
 * @输出格式: [返回值的类型和格式]
 * @边界情况: [特殊情况的处理方式]
 * @性能考虑: [如果有性能相关的考虑]
 */
```

**路由配置模板:**
```typescript
/**
 * @功能概述: [路由的功能和对应页面]
 * @路径参数: [动态路径参数说明]
 * @权限要求: [访问权限或认证要求]
 * @懒加载: [是否使用懒加载及原因]
 * @重定向: [重定向逻辑说明]
 */
```

#### 复杂代码段注释

对于函数内部的复杂逻辑段，使用分步说明：

```javascript
/**
 * @分步说明: [复杂逻辑段的总体描述]
 * 
 *   1. [主要步骤描述]
 *       1.1. [具体子步骤]
 *       1.2. [具体子步骤]
 *   2. [主要步骤描述]
 *       2.1. [具体子步骤]
 */
// --- 复杂代码段开始 ---
// 步骤 1.1: [对应具体实现]
// 步骤 1.2: [对应具体实现]
// --- 复杂代码段结束 ---
```

### 2. 行内注释 (Inline Comments)

用于解释单行或小段代码的具体逻辑。

#### 使用场景
*   解释复杂的表达式、算法或非显而易见的逻辑
*   说明特定选择或"魔法值"的原因
*   标记临时解决方案 (`TODO`、`FIXME`)
*   解释关键变量的含义
*   异步操作的流程说明
*   **函数内部步骤标注**: 使用 `// 步骤 X:` 格式标记主要逻辑步骤
*   **日志语句说明**: 对所有 `logger.xxx()` 语句添加行内注释

#### 行内注释示例

```typescript
// 步骤 1: 验证用户输入的表单数据
const allowedFormats = ['jpg', 'png', 'gif']; // 支持的图片格式列表
if (!allowedFormats.includes(fileExtension)) {
    console.warn(`[步骤 1.1][WARN] 不支持的文件格式: ${fileExtension}`); // 记录不支持格式的警告
    setError('不支持的文件格式'); // 设置错误状态
    return;
}

// 步骤 2: 初始化组件状态
const [isLoading, setIsLoading] = useState(false); // 加载状态管理
console.info(`[步骤 2] 组件状态初始化完成`); // 记录状态初始化

// 等待异步API调用完成
const response = await fetchUserData(userId); // API请求操作
```

## 通用原则

*   **清晰胜于冗余**: 优先编写清晰、自解释的代码，注释作为辅助
*   **关注"为什么"**: 注释不仅说明代码"做什么"，更要解释"为什么"这么做
*   **保持更新**: 修改代码时，务必同步更新相关注释
*   **分层应用**: 根据注释优先级策略，合理分配注释详细程度
*   **模板化**: 使用项目提供的注释模板，保持一致性

## 调试与追踪日志 (Debugging and Tracing Logs)

**注意：React/TypeScript项目的日志规范**

除了上述规范化的代码块注释和行内注释外，在开发和调试过程中，使用详细的追踪日志至关重要。这有助于理解代码的实际执行流程、诊断问题和监控关键操作。请务必使用中文记录日志。

*   目标:
    *   清晰展示代码执行的关键步骤和分支。
    *   记录重要的数据状态（输入、中间处理、输出）。
    *   在出现问题时，能够快速定位错误来源和上下文。
*   主要工具: 在React/TypeScript项目中，使用浏览器控制台API或专门的日志库：
    ```typescript
    // 开发环境使用浏览器控制台
    console.log(), console.info(), console.warn(), console.error()

    // 生产环境可选择专门的日志库
    import logger from '@/lib/logger'; // 根据实际配置调整
    ```
*   日志位置: 开发环境记录到浏览器控制台，生产环境可配置发送到日志服务或本地存储。

### 日志结构化最佳实践

1.  统一模块/组件前缀 (Consistent Module/Component Prefix):
    *   为每个主要组件、Hook或函数定义一个独特且一致的日志前缀，必须严格遵循以下格式：
    *   规范格式: `[文件：文件名.tsx][文件名中文翻译][函数名][SessionID:${sessionId || 'unknown'}]`
    *   格式说明:
        *   `[文件：文件名.tsx]`: 当前文件的名称，包含扩展名
        *   `[文件名中文翻译]`: 文件名的中文含义，如 `BBCNewsOpening.tsx` 翻译为 `BBC新闻开场组件`
        *   `[函数名]`: 当前执行的函数名，如 `handlePlayAnimation`
        *   `[SessionID:${sessionId || 'unknown'}]`: 会话ID，用于追踪用户操作流程
    *   示例:
        ```typescript
        // 在 BBCNewsOpening.tsx 文件中，handlePlayAnimation 函数内定义日志前缀
        const logPrefix = `[文件：BBCNewsOpening.tsx][BBC新闻开场组件][handlePlayAnimation][SessionID:${sessionId || 'unknown'}] `;

        // 在 useApiCall.ts 文件中，fetchData 函数内定义日志前缀
        const logPrefix = `[文件：useApiCall.ts][API调用Hook][fetchData][SessionID:${sessionId || 'unknown'}] `;

        // 对于组件级别的日志（函数外部），可使用组件初始化作为函数名部分
        const componentLogPrefix = `[文件：EnglishLearningPage.tsx][英语学习页面][组件初始化]`;
        ```
    *   目的: 确保所有日志具有统一且清晰的标识，便于在大量日志中筛选和识别特定组件的输出，同时提供文件和函数的准确上下文信息。

2.  分步编号与清晰描述 (Step Numbering and Clear Descriptions):
    *   对一个复杂流程中的关键执行步骤使用层级化编号。
    *   重要：日志中的步骤编号应与代码注释中（无论是函数/方法级别的 `@执行流程`，还是内部复杂代码块的 `@分步说明` 或详细的行内注释，特别是指导性的 `// 步骤 X:` 注释）对应的步骤编号保持一致或清晰关联。这确保了通过阅读日志可以快速定位到代码中相应的解释和逻辑。
    *   示例:
        ```typescript
        // 步骤 1: 校验用户输入参数的有效性...
        console.info(`${logPrefix}[步骤 1] 开始处理请求。`);
        // ...
        // 步骤 2: 根据 userId 从API异步查询用户数据。
        console.info(`${logPrefix}[步骤 2] 验证输入数据。`);
        console.debug(`${logPrefix}[步骤 2.1] 检查字段 'name'。`);
        // ...
        console.warn(`${logPrefix}[步骤 2.5.1] 数据验证失败：...`);
        ```
    *   目的: 清晰展示执行路径，尤其在有多个分支或条件判断时，能快速看出代码走了哪个分支，以及在哪里中断。

3.  记录关键数据状态 (Logging Key Data States):
    *   输入参数: 记录从props、state或函数参数中获取的核心数据及其类型。
        ```typescript
        // 在React组件中记录props和state
        const userId = props.userId;
        console.info(`${logPrefix}[步骤 1.1] 接收到的用户ID: ${userId}`); // 日志：记录接收到的用户ID。
        const formData = state.formData;
        // 对对象或数组使用 JSON.stringify 记录
        console.debug(`${logPrefix}[步骤 1.2] 表单数据内容: ${JSON.stringify(formData)}`); // 日志：记录表单数据的完整内容（调试级别）。
        ```
    *   重要变量: 记录影响逻辑走向或重要的中间计算结果。
    *   复杂数据结构: 使用 `JSON.stringify()` 来记录数组或对象的结构和内容，并考虑使用日志库提供的对象日志功能（如果支持）。
        ```typescript
        const processedResult = { success: true, data: { id: 123, status: 'completed' } };
        // 直接记录对象，日志库可能会自动序列化
        console.debug(`${logPrefix}[步骤 4.1] 处理结果对象: ${JSON.stringify(processedResult)}`); // 日志：记录处理结果对象的完整内容（调试级别）。
        // 记录数组示例
        const dataArray = ['item1', 'item2'];
        console.debug(`${logPrefix}[步骤 4.2] 数据数组: ${JSON.stringify(dataArray)}`); // 日志：记录数据数组的内容（调试级别）。
        ```
    *   目的: 验证数据在处理过程中的正确性，以及在出错时了解当时的数据状态。

4.  上下文信息 (Contextual Information):
    *   在日志中包含关键的上下文标识，如会话 ID、用户 ID、组件 ID、操作类型等。通常作为日志前缀的一部分。
    *   示例: `console.info(`${logPrefix}[步骤 3] 用户ID: ${userId} 尝试更新配置。配置项: ${configKey}`);` // 日志：记录用户尝试更新配置的操作及相关上下文。
    *   目的: 能够将单条日志与特定的用户会话、组件实例或业务操作关联起来。

5.  明确记录操作结果 (Logging Operation Outcomes):
    *   成功标记: 清晰记录关键操作的成功完成，通常使用 `info` 或 `debug` 级别。
        ```typescript
        console.info(`${logPrefix}[步骤 6.1][SUCCESS] 数据保存成功。记录ID: ${newRecordId}`); // 日志：明确记录数据保存操作成功及其生成的记录ID。
        ```
    *   失败/错误标记: 记录操作失败、参数验证不通过或任何不符合预期的情况，使用 `warn` 或 `error` 级别。
        ```typescript
        console.warn(`${logPrefix}[步骤 2.3][WARN] 参数验证失败：字段 'email' 格式错误。`); // 日志：记录参数验证失败的具体字段和原因（警告级别）。
        console.error(`${logPrefix}[步骤 5.1][ERROR] API调用失败：${apiError.message}`); // 日志：记录API调用失败的具体错误信息（错误级别）。
        ```
    *   异常捕获: 在 `try-catch` 块中，务必使用 `error` 级别记录捕获到的异常的详细信息，包括异常消息和堆栈跟踪。
        ```typescript
        try {
            // ... 业务逻辑 ...
        } catch (error) {
            // 记录错误信息和堆栈跟踪
            console.error(`${logPrefix}[UNEXPECTED_EXCEPTION] 发生未预期错误: ${error.message}
Stack: ${error.stack}`); // 日志：记录捕获到的未预期异常的详细消息和堆栈。
            // ... 设置错误状态 ...
        }
        ```
    *   目的: 快速识别操作是否成功，以及失败的具体原因和位置。

6.  日志标记/级别 (Log Markers/Levels):
    *   日志库（如 Winston）直接支持日志级别。使用不同的方法 (`.debug()`, `.info()`, `.warn()`, `.error()`) 来标记日志的重要性。
    *   可以在日志消息内容中添加额外的标记，但主要依靠日志级别进行筛选。
    *   目的: 提高日志的可读性和可筛选性。

### 示例：综合应用场景

#### React组件完整示例

```typescript
/**
 * @功能概述: BBC新闻开场动画组件，播放新闻片头动画
 * @props: { sessionId?: string, onAnimationComplete?: () => void }
 * @状态管理: useState管理播放状态和动画进度
 * @副作用: useEffect监听动画完成事件
 * @渲染逻辑: 条件渲染播放按钮和动画容器
 */
const BBCNewsOpening: React.FC<BBCNewsOpeningProps> = ({ sessionId, onAnimationComplete }) => {
    const [isPlaying, setIsPlaying] = useState(false); // 播放状态管理
    const [progress, setProgress] = useState(0); // 动画进度状态
    const logPrefix = `[文件：BBCNewsOpening.tsx][BBC新闻开场组件][BBCNewsOpening][SessionID:${sessionId || 'unknown'}] `;

    console.info(`${logPrefix}[步骤 1] 组件初始化完成`); // 记录组件初始化

    /**
     * @功能概述: 处理播放动画的用户交互
     * @执行流程: 验证状态 → 启动动画 → 更新进度 → 完成回调
     */
    const handlePlayAnimation = useCallback(async () => {
        const functionLogPrefix = `${logPrefix}[handlePlayAnimation] `;
        console.info(`${functionLogPrefix}[步骤 1] 开始播放动画`); // 记录动画开始

        try {
            // 步骤 1: 验证播放状态
            if (isPlaying) {
                console.warn(`${functionLogPrefix}[步骤 1.1][WARN] 动画已在播放中`); // 记录重复播放警告
                return;
            }

            setIsPlaying(true); // 设置播放状态
            console.debug(`${functionLogPrefix}[步骤 1.2] 播放状态已更新: true`); // 记录状态更新

            // 步骤 2: 执行动画序列
            const animationDuration = 3000; // 动画持续时间
            console.info(`${functionLogPrefix}[步骤 2] 开始执行动画序列，持续时间: ${animationDuration}ms`); // 记录动画参数

            await playAnimationSequence(animationDuration, setProgress); // 执行动画
            console.info(`${functionLogPrefix}[步骤 2][SUCCESS] 动画序列执行完成`); // 记录动画完成

            // 步骤 3: 完成回调
            if (onAnimationComplete) {
                onAnimationComplete(); // 调用完成回调
                console.debug(`${functionLogPrefix}[步骤 3] 动画完成回调已执行`); // 记录回调执行
            }

        } catch (error) {
            console.error(`${functionLogPrefix}[UNEXPECTED_EXCEPTION] 动画播放失败: ${error.message}
Stack: ${error.stack}`); // 记录动画播放异常
            setIsPlaying(false); // 重置播放状态
        }
    }, [isPlaying, sessionId, onAnimationComplete]);

    return (
        <div className="news-opening-container">
            {!isPlaying && (
                <button onClick={handlePlayAnimation} className="play-button">
                    播放新闻开场
                </button>
            )}
            {isPlaying && (
                <div className="animation-container">
                    <div className="progress-bar" style={{ width: `${progress}%` }} />
                </div>
            )}
        </div>
    );
};
```

#### 自定义Hook完整示例

```typescript
/**
 * @功能概述: 管理API调用状态和数据获取的自定义Hook
 * @参数说明: url - API端点URL, options - 请求配置选项
 * @返回值: { data, loading, error, refetch } - 数据、加载状态、错误信息和重新获取函数
 * @依赖项: 依赖fetch API和useState、useEffect Hook
 * @使用示例: const { data, loading } = useApiCall('/api/users')
 */
const useApiCall = <T>(url: string, options?: RequestInit): ApiCallResult<T> => {
    const [data, setData] = useState<T | null>(null); // API响应数据状态
    const [loading, setLoading] = useState(false); // 加载状态
    const [error, setError] = useState<string | null>(null); // 错误状态
    const sessionId = useSessionId(); // 获取会话ID
    const logPrefix = `[文件：useApiCall.ts][API调用Hook][useApiCall][SessionID:${sessionId || 'unknown'}] `;

    console.info(`${logPrefix}[步骤 1] Hook初始化完成，URL: ${url}`); // 记录Hook初始化

    /**
     * @功能概述: 执行API调用的核心函数
     * @执行流程: 设置加载状态 → 发起请求 → 处理响应 → 更新状态
     */
    const fetchData = useCallback(async () => {
        const functionLogPrefix = `${logPrefix}[fetchData] `;
        console.info(`${functionLogPrefix}[步骤 1] 开始API调用`); // 记录API调用开始

        try {
            // 步骤 1: 设置加载状态
            setLoading(true); // 设置加载中状态
            setError(null); // 清除之前的错误
            console.debug(`${functionLogPrefix}[步骤 1.1] 加载状态已设置为true`); // 记录状态更新

            // 步骤 2: 发起HTTP请求
            const requestOptions = { ...options }; // 合并请求选项
            console.debug(`${functionLogPrefix}[步骤 2] 请求配置: ${JSON.stringify(requestOptions)}`); // 记录请求配置

            const response = await fetch(url, requestOptions); // 发起API请求
            console.info(`${functionLogPrefix}[步骤 2] HTTP请求完成，状态码: ${response.status}`); // 记录响应状态

            // 步骤 3: 处理响应
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`); // 抛出HTTP错误
            }

            const responseData = await response.json(); // 解析JSON响应
            console.debug(`${functionLogPrefix}[步骤 3] 响应数据: ${JSON.stringify(responseData)}`); // 记录响应数据

            setData(responseData); // 更新数据状态
            console.info(`${functionLogPrefix}[步骤 3][SUCCESS] API调用成功完成`); // 记录成功

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误'; // 提取错误信息
            console.error(`${functionLogPrefix}[UNEXPECTED_EXCEPTION] API调用失败: ${errorMessage}
Stack: ${error instanceof Error ? error.stack : 'N/A'}`); // 记录API调用异常
            setError(errorMessage); // 设置错误状态
        } finally {
            setLoading(false); // 重置加载状态
            console.debug(`${functionLogPrefix}[步骤 4] 加载状态已重置为false`); // 记录状态重置
        }
    }, [url, options, sessionId]);

    // 组件挂载时自动调用API
    useEffect(() => {
        fetchData(); // 执行数据获取
    }, [fetchData]);

    return { data, loading, error, refetch: fetchData }; // 返回Hook状态和方法
};
```

### React/TypeScript 代码注释与日志规范总结

*   **注释风格**: 使用 JSDoc 块注释 (`/** ... */`) 和结构化标签，包括项目特定的注释模板
*   **优先级策略**: 根据代码重要性分层应用注释，确保核心功能有完整注释
*   **模板化**: 使用React组件、自定义Hook、API函数等专用注释模板，保持一致性
*   **行内注释**: 对复杂逻辑、异步操作、关键步骤和所有日志语句添加说明
*   **日志记录**: 使用浏览器控制台或专门日志库，严格遵循日志前缀和步骤编号规范
*   **步骤对应**: 确保代码注释中的步骤编号与日志记录中的步骤编号一致
*   **上下文追踪**: 在日志中记录关键数据状态和上下文信息，便于问题定位

请 AI 严格遵循以上注释规范，特别是针对React/TypeScript项目的特定要求，以提高代码库的整体质量和可维护性。

