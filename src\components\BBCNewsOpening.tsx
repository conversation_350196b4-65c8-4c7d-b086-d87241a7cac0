import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import backgroundImage from '@/assets/classroom-background.jpg';

/**
 * @功能概述: BBC新闻开场动画组件，播放新闻片头动画序列
 * @状态管理: useRef管理DOM元素引用，用于GSAP动画控制
 * @副作用: useEffect在组件挂载时启动4秒动画序列
 * @渲染逻辑: 渲染BBC新闻开场界面，包含标题、倒计时和背景
 */
const BBCNewsOpening = () => {
  // DOM元素引用，用于GSAP动画控制
  const containerRef = useRef<HTMLDivElement>(null); // 主容器引用
  const bbcLogoRef = useRef<HTMLDivElement>(null); // BBC标志引用
  const chineseTitleRef = useRef<HTMLDivElement>(null); // 中文标题引用
  const englishTitleRef = useRef<HTMLDivElement>(null); // 英文标题引用
  const chineseSubtitleRef = useRef<HTMLDivElement>(null); // 中文副标题引用
  const overlayRef = useRef<HTMLDivElement>(null); // 遮罩层引用

  // 倒计时相关元素引用
  const countdownRef = useRef<HTMLDivElement>(null); // 倒计时容器引用
  const countdownNumberRef = useRef<HTMLDivElement>(null); // 倒计时数字引用

  /**
   * @功能概述: 组件挂载时启动BBC新闻开场动画序列
   * @执行流程: 初始化元素状态 → 执行4秒动画时间线 → 清理资源
   */
  useEffect(() => {
    // 步骤 1: 验证容器元素是否存在
    if (!containerRef.current) return;

    const tl = gsap.timeline(); // 创建GSAP时间线

    // 步骤 2: 设置所有元素的初始状态
    // 主要元素从左侧飞入，带有浮动效果
    gsap.set([bbcLogoRef.current, chineseTitleRef.current, englishTitleRef.current, chineseSubtitleRef.current], {
      opacity: 0, // 初始透明
      x: -200, // 从左侧200px位置开始
      y: -20, // 轻微向上偏移
      rotation: -5, // 轻微逆时针旋转
      scale: 0.9 // 稍微缩小
    });

    // 设置遮罩层初始透明度
    gsap.set(overlayRef.current, {
      opacity: 0.3 // 初始半透明状态
    });

    // 设置倒计时元素初始状态
    gsap.set([countdownRef.current, countdownNumberRef.current], {
      opacity: 0, // 初始完全透明
      scale: 0 // 初始缩放为0
    });

    // 步骤 3: 执行动画序列（总时长4秒）
    tl
      // 步骤 3.1: 0-0.2秒 - 快速遮罩层淡入
      .to(overlayRef.current, {
        opacity: 0.6, // 增加遮罩透明度
        duration: 0.2, // 动画持续0.2秒
        ease: "power2.out" // 缓动效果
      })
      // 步骤 3.2: 0-1秒 - 所有元素从左侧依次飞入
      // BBC标志首先出现（0秒开始）
      .to(bbcLogoRef.current, {
        opacity: 1, // 完全显示
        x: 0, // 移动到原位置
        y: 0, // 移动到原位置
        rotation: 0, // 恢复正常角度
        scale: 1, // 恢复正常大小
        duration: 1, // 动画持续1秒
        ease: "power3.out" // 缓动效果
      }, 0)
      // 中文标题（0.1秒开始）
      .to(chineseTitleRef.current, {
        opacity: 1, // 完全显示
        x: 0, // 移动到原位置
        y: 0, // 移动到原位置
        rotation: 0, // 恢复正常角度
        scale: 1, // 恢复正常大小
        duration: 1, // 动画持续1秒
        ease: "power3.out" // 缓动效果
      }, 0.1)
      // 英文标题（0.2秒开始）
      .to(englishTitleRef.current, {
        opacity: 1, // 完全显示
        x: 0, // 移动到原位置
        y: 0, // 移动到原位置
        rotation: 0, // 恢复正常角度
        scale: 1, // 恢复正常大小
        duration: 1, // 动画持续1秒
        ease: "power3.out" // 缓动效果
      }, 0.2)
      // 中文副标题（0.3秒开始）
      .to(chineseSubtitleRef.current, {
        opacity: 1, // 完全显示
        x: 0, // 移动到原位置
        y: 0, // 移动到原位置
        rotation: 0, // 恢复正常角度
        scale: 1, // 恢复正常大小
        duration: 1, // 动画持续1秒
        ease: "power3.out" // 缓动效果
      }, 0.3)
      // 步骤 3.3: 1秒 - 显示倒计时容器
      .to(countdownRef.current, {
        opacity: 1, // 完全显示
        scale: 1, // 恢复正常大小
        duration: 0.4, // 动画持续0.4秒
        ease: "elastic.out(1, 0.8)" // 弹性缓动效果
      }, 1)
      // 步骤 3.4: 1.1秒开始 - 倒计时动画（3、2、1）
      .call(() => {
        let count = 3; // 倒计时起始数字
        /**
         * @功能概述: 更新倒计时数字并执行动画
         * @执行流程: 更新数字文本 → 执行旋转缩放动画 → 递减计数 → 延时递归调用
         */
        const updateCountdown = () => {
          if (countdownNumberRef.current) {
            countdownNumberRef.current.textContent = count.toString(); // 更新倒计时数字文本
            // 数字出现动画：从旋转缩放0到正常状态
            gsap.fromTo(countdownNumberRef.current,
              { scale: 0, opacity: 0, rotation: -180 }, // 起始状态：缩放0，透明，逆时针旋转180度
              {
                scale: 1, // 恢复正常大小
                opacity: 1, // 完全显示
                rotation: 0, // 恢复正常角度
                duration: 0.6, // 动画持续0.6秒
                ease: "elastic.out(1, 0.7)" // 弹性缓动效果
              }
            );
          }
          count--; // 递减计数
          if (count >= 0) {
            setTimeout(updateCountdown, 1000); // 1秒后继续下一个数字
          }
        };
        updateCountdown(); // 开始倒计时
      }, null, 1.1)
      // 步骤 3.5: 4秒 - 页面消失特效
      .to(containerRef.current, {
        scale: 0.3, // 缩小到30%
        opacity: 0, // 完全透明
        rotation: 360, // 顺时针旋转360度
        duration: 0.8, // 动画持续0.8秒
        ease: "power3.in" // 加速缓动效果
      }, 4)
      // 步骤 3.6: 4.3秒 - 设置页面背景为纯黑色
      .set("body", {
        backgroundColor: "#000000" // 设置背景色为黑色
      }, 4.3);

    // 步骤 4: 清理函数 - 组件卸载时销毁动画时间线
    return () => {
      tl.kill(); // 销毁GSAP时间线，防止内存泄漏
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="relative w-full h-screen overflow-hidden bg-news-bg"
      style={{ aspectRatio: '16/9' }} // 设置16:9宽高比
    >
      {/* 背景图片层 */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${backgroundImage})`, // 设置背景图片
          filter: 'brightness(0.7) contrast(1.1)' // 调整亮度和对比度
        }}
      />

      {/* 深色遮罩层 */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-transparent"
      />

      {/* 内容容器 */}
      <div className="absolute inset-0 flex flex-col justify-center px-8 md:px-16 lg:px-24">

        {/* BBC标志区域 */}
        <div ref={bbcLogoRef} className="mb-6">
          <div className="flex items-center">
            <div className="bg-bbc-red px-6 py-2 mr-4">
              <span className="text-bbc-white font-bold text-2xl md:text-3xl lg:text-4xl tracking-wider">
                BBC NEWS
              </span>
            </div>
            <div className="h-1 bg-bbc-white flex-1 max-w-xs"></div> {/* 装饰性白线 */}
          </div>
        </div>

        {/* 中文主标题 */}
        <div ref={chineseTitleRef} className="mb-4">
          <h1 className="text-bbc-yellow font-bold text-4xl md:text-6xl lg:text-7xl leading-tight">
            BBC英语精听
          </h1>
        </div>

        {/* 英文标题 */}
        <div ref={englishTitleRef} className="mb-6">
          <h2 className="text-bbc-white font-semibold text-xl md:text-2xl lg:text-3xl max-w-2xl leading-relaxed">
            UK releases New RSHE Guidance for Students
          </h2>
        </div>

        {/* 中文副标题 */}
        <div ref={chineseSubtitleRef}>
          <h3 className="text-bbc-blue font-medium text-2xl md:text-3xl lg:text-4xl">
            英国新版学生性教育指南
          </h3>
        </div>
      </div>

      {/* 倒计时器 */}
      <div
        ref={countdownRef}
        className="absolute top-8 right-8 flex items-center justify-center w-24 h-24 bg-bbc-red/90 backdrop-blur-sm rounded-full border-4 border-bbc-white/30 shadow-lg"
      >
        <div
          ref={countdownNumberRef}
          className="text-bbc-white font-bold text-4xl tracking-wider"
        >
          3 {/* 倒计时初始数字 */}
        </div>
      </div>

      {/* 底部渐变效果 */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/60 to-transparent" />
    </div>
  );
};

export default BBCNewsOpening;