---
type: "always_apply"
description: "Augment开发规则和架构变更铁律"
---
# Augment 开发规则和架构变更铁律

## 🚨 技术架构变更铁律 - 必须严格遵守！

### ⚠️ 重要警告：架构文档同步是强制性要求！

**这是一条铁律，任何违反都会导致严重的团队协作问题！**

### 📋 强制同步规则

#### 规则1：项目结构变更时的强制同步

**触发条件**：当项目中发生以下任何变更时
- 新增/删除/重命名任何目录
- 新增/删除/重命名任何重要文件
- 修改目录结构或文件组织方式

**强制操作**：必须立即更新 `.augment\rules\PROJECT_ARCHITECTURE.md` 中的以下部分：

1. **`## 📁 项目结构详解`**
   - **`### 完整目录结构`** - 必须与当前代码库100%同步
   - **`### 核心目录详解`** - 必须添加新增目录的说明，删除已移除目录的说明

**示例**：
```
如果新增了 src/services/ 目录：
1. 在"完整目录结构"中添加该目录
2. 在"核心目录详解"中添加该目录的功能说明
```

#### 规则2：技术栈变更时的强制同步

**触发条件**：当项目中发生以下任何变更时
- 新增/删除/替换任何技术栈组件
- 升级主要依赖版本
- 修改构建工具或开发工具
- 更改UI框架或样式框架

**强制操作**：必须立即更新 `PROJECT_ARCHITECTURE.md` 中的以下部分：

1. **`## 🛠️ 技术栈架构`**
   - **核心技术层图表** - 必须反映当前技术栈
   - **技术选型说明** - 必须与当前使用的技术完全一致

**示例**：
```
如果从Tailwind CSS切换到Styled Components：
1. 更新技术栈架构图
2. 更新技术选型说明
3. 更新相关的代码示例
```

#### 规则3：配置文件变更时的强制同步

**触发条件**：当项目中发生以下任何变更时
- 修改 vite.config.ts
- 修改 tailwind.config.js
- 修改 tsconfig.json
- 新增/删除配置文件

**强制操作**：必须立即更新 `PROJECT_ARCHITECTURE.md` 中的配置文件说明部分

### 🔄 同步检查清单

**每次架构变更后，必须检查以下文档是否需要更新**：

- [ ] `PROJECT_ARCHITECTURE.md` - 项目架构文档
- [ ] `NEWBIE_DEVELOPER_GUIDE.md` - 新手开发指南

### 💡 最佳实践

1. **变更前规划**：在进行架构变更前，先规划需要更新的文档
2. **同步变更**：架构变更和文档更新应该在同一个提交中完成
3. **团队通知**：重大架构变更应该通知所有团队成员
4. **版本记录**：在文档中记录重要的架构变更历史

### 🚫 严禁的行为

- ❌ 修改项目结构但不更新文档
- ❌ 更换技术栈但不更新架构说明
- ❌ 延迟文档更新，导致文档与代码不同步
- ❌ 部分更新文档，遗漏某些章节

### ⚡ 紧急情况处理

如果发现文档与代码库不同步：
1. **立即停止开发工作**
2. **优先更新文档**
3. **验证文档准确性**
4. **通知团队成员**

---

**记住**：文档同步不是可选项，而是强制要求！保持文档与代码的一致性是团队协作的基础。
