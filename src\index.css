@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 13% 18%;
    --foreground: 0 0% 100%;

    --card: 220 13% 18%;
    --card-foreground: 0 0% 100%;

    --popover: 220 13% 18%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 84% 52%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 13% 25%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 13% 25%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 48 100% 50%;
    --accent-foreground: 220 13% 18%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 25%;
    --input: 220 13% 25%;
    --ring: 0 84% 52%;

    --radius: 0.5rem;
    
    /* BBC News specific colors */
    --bbc-red: 0 84% 52%;
    --bbc-yellow: 48 100% 50%;
    --bbc-blue: 204 100% 60%;
    --bbc-white: 0 0% 100%;
    --news-bg: 220 13% 18%;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 13% 18%;
    --foreground: 0 0% 100%;

    --card: 220 13% 18%;
    --card-foreground: 0 0% 100%;

    --popover: 220 13% 18%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 84% 52%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 13% 25%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 13% 25%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 48 100% 50%;
    --accent-foreground: 220 13% 18%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 25%;
    --input: 220 13% 25%;
    --ring: 0 84% 52%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}